#!/usr/bin/env python3
"""
Test script to verify enhanced live zone detection features
"""

import pandas as pd
import os
from multi_timeframe_live_fetcher import (
    get_existing_zone_count,
    prepare_zones_for_tomorrow,
    TIMEFRAMES
)

def test_enhanced_features():
    """Test the new enhanced features"""
    print("🧪 Testing Enhanced Live Zone Detection Features...")
    
    # Test 1: Check existing zone count function
    print("\n📊 Test 1: Existing Zone Count")
    for timeframe in TIMEFRAMES.keys():
        tf_name = TIMEFRAMES[timeframe]['name']
        count = get_existing_zone_count(timeframe)
        print(f"  {tf_name}: {count} existing zones")
    
    # Test 2: Check if Tomorrow_Zone_Continuation folder can be created
    print("\n📅 Test 2: Tomorrow Zone Continuation Setup")
    tomorrow_folder = "Tomorrow_Zone_Continuation"
    if not os.path.exists(tomorrow_folder):
        os.makedirs(tomorrow_folder)
        print(f"  ✅ Created folder: {tomorrow_folder}")
    else:
        print(f"  ✅ Folder already exists: {tomorrow_folder}")
    
    # Test 3: Check timeframe configurations
    print("\n⚙️ Test 3: Timeframe Configurations")
    for timeframe, config in TIMEFRAMES.items():
        tf_name = config['name']
        interval_sec = config.get('interval_seconds', 'NOT SET')
        print(f"  {tf_name}: {interval_sec} seconds interval")
    
    # Test 4: Check if enhanced CSV structure is ready
    print("\n📋 Test 4: Enhanced CSV Structure Check")
    historical_files = [
        "Historical_Data_Layer/historical_1min.csv",
        "Historical_Data_Layer/historical_5min.csv",
        "Historical_Data_Layer/historical_60min.csv"
    ]
    
    for file_path in historical_files:
        if os.path.exists(file_path):
            df = pd.read_csv(file_path, nrows=1)  # Read just header
            has_wma = 'wma5_10' in df.columns
            has_price_active = 'PriceActive' in df.columns
            print(f"  {file_path}: WMA5_10={has_wma}, PriceActive={has_price_active}")
        else:
            print(f"  {file_path}: File not found")
    
    print("\n🎉 Enhanced Features Test Completed!")

if __name__ == "__main__":
    test_enhanced_features()
