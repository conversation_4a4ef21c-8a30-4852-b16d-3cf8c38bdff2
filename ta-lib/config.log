This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by ta-lib configure 0.4.0, which was
generated by GNU Autoconf 2.61.  Invocation command line was

  $ ./configure --prefix=/usr/local

## --------- ##
## Platform. ##
## --------- ##

hostname = raghunandansrinivas-HP-ZBook-15-G5
uname -m = x86_64
uname -r = 6.14.0-15-generic
uname -s = Linux
uname -v = #15-Ubuntu SMP PREEMPT_DYNAMIC Sun Apr  6 15:05:05 UTC 2025

/usr/bin/uname -p = x86_64
/bin/uname -X     = unknown

/bin/arch              = x86_64
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
/usr/bin/hostinfo      = unknown
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /home/<USER>/.local/bin
PATH: /home/<USER>/.local/bin
PATH: /usr/local/sbin
PATH: /usr/local/bin
PATH: /usr/sbin
PATH: /usr/bin
PATH: /sbin
PATH: /bin
PATH: /usr/games
PATH: /usr/local/games
PATH: /snap/bin
PATH: /snap/bin
PATH: /home/<USER>/.vscode/extensions/ms-python.debugpy-2025.8.0-linux-x64/bundled/scripts/noConfigScripts
PATH: /home/<USER>/.config/Code/User/globalStorage/github.copilot-chat/debugCommand


## ----------- ##
## Core tests. ##
## ----------- ##

configure:1987: checking for a BSD-compatible install
configure:2043: result: /usr/bin/install -c
configure:2054: checking whether build environment is sane
configure:2097: result: yes
configure:2125: checking for a thread-safe mkdir -p
configure:2164: result: /usr/bin/mkdir -p
configure:2177: checking for gawk
configure:2207: result: no
configure:2177: checking for mawk
configure:2193: found /usr/bin/mawk
configure:2204: result: mawk
configure:2215: checking whether make sets $(MAKE)
configure:2236: result: yes
configure:2474: checking for gcc
configure:2490: found /usr/bin/gcc
configure:2501: result: gcc
configure:2739: checking for C compiler version
configure:2746: gcc --version >&5
gcc (Ubuntu 14.2.0-19ubuntu2) 14.2.0
Copyright (C) 2024 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:2749: $? = 0
configure:2756: gcc -v >&5
Using built-in specs.
COLLECT_GCC=gcc
COLLECT_LTO_WRAPPER=/usr/libexec/gcc/x86_64-linux-gnu/14/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 14.2.0-19ubuntu2' --with-bugurl=file:///usr/share/doc/gcc-14/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2,rust --prefix=/usr --with-gcc-major-version-only --program-suffix=-14 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/libexec --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-libstdcxx-backtrace --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-14-C86vgL/gcc-14-14.2.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-14-C86vgL/gcc-14-14.2.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 14.2.0 (Ubuntu 14.2.0-19ubuntu2) 
configure:2759: $? = 0
configure:2766: gcc -V >&5
gcc: error: unrecognized command-line option '-V'
gcc: fatal error: no input files
compilation terminated.
configure:2769: $? = 1
configure:2792: checking for C compiler default output file name
configure:2819: gcc    conftest.c  >&5
configure:2822: $? = 0
configure:2860: result: a.out
configure:2877: checking whether the C compiler works
configure:2887: ./a.out
configure:2890: $? = 0
configure:2907: result: yes
configure:2914: checking whether we are cross compiling
configure:2916: result: no
configure:2919: checking for suffix of executables
configure:2926: gcc -o conftest    conftest.c  >&5
configure:2929: $? = 0
configure:2953: result: 
configure:2959: checking for suffix of object files
configure:2985: gcc -c   conftest.c >&5
configure:2988: $? = 0
configure:3011: result: o
configure:3015: checking whether we are using the GNU C compiler
configure:3044: gcc -c   conftest.c >&5
configure:3050: $? = 0
configure:3067: result: yes
configure:3072: checking whether gcc accepts -g
configure:3102: gcc -c -g  conftest.c >&5
configure:3108: $? = 0
configure:3207: result: yes
configure:3224: checking for gcc option to accept ISO C89
configure:3298: gcc  -c -g -O2  conftest.c >&5
configure:3304: $? = 0
configure:3327: result: none needed
configure:3356: checking for style of include used by make
configure:3384: result: GNU
configure:3409: checking dependency style of gcc
configure:3500: result: gcc3
configure:3593: checking build system type
configure:3611: result: x86_64-unknown-linux-gnu
configure:3633: checking host system type
configure:3648: result: x86_64-unknown-linux-gnu
configure:3670: checking for a sed that does not truncate output
configure:3724: result: /usr/bin/sed
configure:3727: checking for grep that handles long lines and -e
configure:3801: result: /usr/bin/grep
configure:3806: checking for egrep
configure:3884: result: /usr/bin/grep -E
configure:3900: checking for ld used by gcc
configure:3967: result: /usr/bin/ld
configure:3976: checking if the linker (/usr/bin/ld) is GNU ld
configure:3991: result: yes
configure:3996: checking for /usr/bin/ld option to reload object files
configure:4003: result: -r
configure:4021: checking for BSD-compatible nm
configure:4070: result: /usr/bin/nm -B
configure:4074: checking whether ln -s works
configure:4078: result: yes
configure:4085: checking how to recognise dependent libraries
configure:4261: result: pass_all
configure:4349: gcc -c -g -O2  conftest.c >&5
configure:4352: $? = 0
configure:4495: checking how to run the C preprocessor
configure:4535: gcc -E  conftest.c
configure:4541: $? = 0
configure:4572: gcc -E  conftest.c
conftest.c:10:10: fatal error: ac_nonexistent.h: No such file or directory
   10 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:4578: $? = 1
configure: failed program was:
| /* confdefs.h.  */
| #define PACKAGE_NAME "ta-lib"
| #define PACKAGE_TARNAME "ta-lib"
| #define PACKAGE_VERSION "0.4.0"
| #define PACKAGE_STRING "ta-lib 0.4.0"
| #define PACKAGE_BUGREPORT "http://sourceforge.net/tracker/?group_id=8903&atid=108903"
| #define PACKAGE "ta-lib"
| #define VERSION "0.4.0"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:4611: result: gcc -E
configure:4640: gcc -E  conftest.c
configure:4646: $? = 0
configure:4677: gcc -E  conftest.c
conftest.c:10:10: fatal error: ac_nonexistent.h: No such file or directory
   10 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:4683: $? = 1
configure: failed program was:
| /* confdefs.h.  */
| #define PACKAGE_NAME "ta-lib"
| #define PACKAGE_TARNAME "ta-lib"
| #define PACKAGE_VERSION "0.4.0"
| #define PACKAGE_STRING "ta-lib 0.4.0"
| #define PACKAGE_BUGREPORT "http://sourceforge.net/tracker/?group_id=8903&atid=108903"
| #define PACKAGE "ta-lib"
| #define VERSION "0.4.0"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:4721: checking for ANSI C header files
configure:4751: gcc -c -g -O2  conftest.c >&5
configure:4757: $? = 0
configure:4856: gcc -o conftest -g -O2   conftest.c  >&5
configure:4859: $? = 0
configure:4865: ./conftest
configure:4868: $? = 0
configure:4885: result: yes
configure:4909: checking for sys/types.h
configure:4930: gcc -c -g -O2  conftest.c >&5
configure:4936: $? = 0
configure:4952: result: yes
configure:4909: checking for sys/stat.h
configure:4930: gcc -c -g -O2  conftest.c >&5
configure:4936: $? = 0
configure:4952: result: yes
configure:4909: checking for stdlib.h
configure:4930: gcc -c -g -O2  conftest.c >&5
configure:4936: $? = 0
configure:4952: result: yes
configure:4909: checking for string.h
configure:4930: gcc -c -g -O2  conftest.c >&5
configure:4936: $? = 0
configure:4952: result: yes
configure:4909: checking for memory.h
configure:4930: gcc -c -g -O2  conftest.c >&5
configure:4936: $? = 0
configure:4952: result: yes
configure:4909: checking for strings.h
configure:4930: gcc -c -g -O2  conftest.c >&5
configure:4936: $? = 0
configure:4952: result: yes
configure:4909: checking for inttypes.h
configure:4930: gcc -c -g -O2  conftest.c >&5
configure:4936: $? = 0
configure:4952: result: yes
configure:4909: checking for stdint.h
configure:4930: gcc -c -g -O2  conftest.c >&5
configure:4936: $? = 0
configure:4952: result: yes
configure:4909: checking for unistd.h
configure:4930: gcc -c -g -O2  conftest.c >&5
configure:4936: $? = 0
configure:4952: result: yes
configure:4979: checking dlfcn.h usability
configure:4996: gcc -c -g -O2  conftest.c >&5
configure:5002: $? = 0
configure:5016: result: yes
configure:5020: checking dlfcn.h presence
configure:5035: gcc -E  conftest.c
configure:5041: $? = 0
configure:5055: result: yes
configure:5088: checking for dlfcn.h
configure:5096: result: yes
configure:5167: checking for g++
configure:5183: found /usr/bin/g++
configure:5194: result: g++
configure:5225: checking for C++ compiler version
configure:5232: g++ --version >&5
g++ (Ubuntu 14.2.0-19ubuntu2) 14.2.0
Copyright (C) 2024 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.

configure:5235: $? = 0
configure:5242: g++ -v >&5
Using built-in specs.
COLLECT_GCC=g++
COLLECT_LTO_WRAPPER=/usr/libexec/gcc/x86_64-linux-gnu/14/lto-wrapper
OFFLOAD_TARGET_NAMES=nvptx-none:amdgcn-amdhsa
OFFLOAD_TARGET_DEFAULT=1
Target: x86_64-linux-gnu
Configured with: ../src/configure -v --with-pkgversion='Ubuntu 14.2.0-19ubuntu2' --with-bugurl=file:///usr/share/doc/gcc-14/README.Bugs --enable-languages=c,ada,c++,go,d,fortran,objc,obj-c++,m2,rust --prefix=/usr --with-gcc-major-version-only --program-suffix=-14 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/libexec --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-bootstrap --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-libstdcxx-backtrace --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --enable-libphobos-checking=release --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --enable-cet --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-14-C86vgL/gcc-14-14.2.0/debian/tmp-nvptx/usr,amdgcn-amdhsa=/build/gcc-14-C86vgL/gcc-14-14.2.0/debian/tmp-gcn/usr --enable-offload-defaulted --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu --with-build-config=bootstrap-lto-lean --enable-link-serialization=2
Thread model: posix
Supported LTO compression algorithms: zlib zstd
gcc version 14.2.0 (Ubuntu 14.2.0-19ubuntu2) 
configure:5245: $? = 0
configure:5252: g++ -V >&5
g++: error: unrecognized command-line option '-V'
g++: fatal error: no input files
compilation terminated.
configure:5255: $? = 1
configure:5258: checking whether we are using the GNU C++ compiler
configure:5287: g++ -c   conftest.cpp >&5
configure:5293: $? = 0
configure:5310: result: yes
configure:5315: checking whether g++ accepts -g
configure:5345: g++ -c -g  conftest.cpp >&5
configure:5351: $? = 0
configure:5450: result: yes
configure:5475: checking dependency style of g++
configure:5566: result: gcc3
configure:5591: checking how to run the C++ preprocessor
configure:5627: g++ -E  conftest.cpp
configure:5633: $? = 0
configure:5664: g++ -E  conftest.cpp
conftest.cpp:21:10: fatal error: ac_nonexistent.h: No such file or directory
   21 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:5670: $? = 1
configure: failed program was:
| /* confdefs.h.  */
| #define PACKAGE_NAME "ta-lib"
| #define PACKAGE_TARNAME "ta-lib"
| #define PACKAGE_VERSION "0.4.0"
| #define PACKAGE_STRING "ta-lib 0.4.0"
| #define PACKAGE_BUGREPORT "http://sourceforge.net/tracker/?group_id=8903&atid=108903"
| #define PACKAGE "ta-lib"
| #define VERSION "0.4.0"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DLFCN_H 1
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:5703: result: g++ -E
configure:5732: g++ -E  conftest.cpp
configure:5738: $? = 0
configure:5769: g++ -E  conftest.cpp
conftest.cpp:21:10: fatal error: ac_nonexistent.h: No such file or directory
   21 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
compilation terminated.
configure:5775: $? = 1
configure: failed program was:
| /* confdefs.h.  */
| #define PACKAGE_NAME "ta-lib"
| #define PACKAGE_TARNAME "ta-lib"
| #define PACKAGE_VERSION "0.4.0"
| #define PACKAGE_STRING "ta-lib 0.4.0"
| #define PACKAGE_BUGREPORT "http://sourceforge.net/tracker/?group_id=8903&atid=108903"
| #define PACKAGE "ta-lib"
| #define VERSION "0.4.0"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DLFCN_H 1
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:5868: checking for g77
configure:5898: result: no
configure:5868: checking for xlf
configure:5898: result: no
configure:5868: checking for f77
configure:5898: result: no
configure:5868: checking for frt
configure:5898: result: no
configure:5868: checking for pgf77
configure:5898: result: no
configure:5868: checking for cf77
configure:5898: result: no
configure:5868: checking for fort77
configure:5898: result: no
configure:5868: checking for fl32
configure:5898: result: no
configure:5868: checking for af77
configure:5898: result: no
configure:5868: checking for xlf90
configure:5898: result: no
configure:5868: checking for f90
configure:5898: result: no
configure:5868: checking for pgf90
configure:5898: result: no
configure:5868: checking for pghpf
configure:5898: result: no
configure:5868: checking for epcf90
configure:5898: result: no
configure:5868: checking for gfortran
configure:5898: result: no
configure:5868: checking for g95
configure:5898: result: no
configure:5868: checking for xlf95
configure:5898: result: no
configure:5868: checking for f95
configure:5898: result: no
configure:5868: checking for fort
configure:5898: result: no
configure:5868: checking for ifort
configure:5898: result: no
configure:5868: checking for ifc
configure:5898: result: no
configure:5868: checking for efc
configure:5898: result: no
configure:5868: checking for pgf95
configure:5898: result: no
configure:5868: checking for lf95
configure:5898: result: no
configure:5868: checking for ftn
configure:5898: result: no
configure:5925: checking for Fortran 77 compiler version
configure:5932:  --version >&5
./configure: line 5933: --version: command not found
configure:5935: $? = 127
configure:5942:  -v >&5
./configure: line 5943: -v: command not found
configure:5945: $? = 127
configure:5952:  -V >&5
./configure: line 5953: -V: command not found
configure:5955: $? = 127
configure:5963: checking whether we are using the GNU Fortran 77 compiler
configure:5982:  -c  conftest.F >&5
./configure: line 5983: -c: command not found
configure:5988: $? = 127
configure: failed program was:
|       program main
| #ifndef __GNUC__
|        choke me
| #endif
| 
|       end
configure:6005: result: no
configure:6011: checking whether  accepts -g
configure:6028:  -c -g conftest.f >&5
./configure: line 6029: -c: command not found
configure:6034: $? = 127
configure: failed program was:
|       program main
| 
|       end
configure:6050: result: no
configure:6080: checking the maximum length of command line arguments
configure:6189: result: 32768
configure:6200: checking command to parse /usr/bin/nm -B output from gcc object
configure:6305: gcc -c -g -O2  conftest.c >&5
configure:6308: $? = 0
configure:6312: /usr/bin/nm -B conftest.o \| sed -n -e 's/^.*[ 	]\([ABCDGIRSTW][ABCDGIRSTW]*\)[ 	][ 	]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p' \> conftest.nm
configure:6315: $? = 0
configure:6367: gcc -o conftest -g -O2   conftest.c conftstm.o >&5
configure:6370: $? = 0
configure:6408: result: ok
configure:6412: checking for objdir
configure:6427: result: .libs
configure:6519: checking for ar
configure:6535: found /usr/bin/ar
configure:6546: result: ar
configure:6615: checking for ranlib
configure:6631: found /usr/bin/ranlib
configure:6642: result: ranlib
configure:6711: checking for strip
configure:6727: found /usr/bin/strip
configure:6738: result: strip
configure:7024: checking if gcc supports -fno-rtti -fno-exceptions
configure:7042: gcc -c -g -O2  -fno-rtti -fno-exceptions conftest.c >&5
cc1: warning: command-line option '-fno-rtti' is valid for C++/D/ObjC++ but not for C
configure:7046: $? = 0
configure:7059: result: no
configure:7074: checking for gcc option to produce PIC
configure:7284: result: -fPIC
configure:7292: checking if gcc PIC flag -fPIC works
configure:7310: gcc -c -g -O2  -fPIC -DPIC conftest.c >&5
configure:7314: $? = 0
configure:7327: result: yes
configure:7355: checking if gcc static flag -static works
configure:7383: result: yes
configure:7393: checking if gcc supports -c -o file.o
configure:7414: gcc -c -g -O2  -o out/conftest2.o conftest.c >&5
configure:7418: $? = 0
configure:7440: result: yes
configure:7466: checking whether the gcc linker (/usr/bin/ld -m elf_x86_64) supports shared libraries
configure:8421: result: yes
configure:8442: checking whether -lc should be explicitly linked in
configure:8447: gcc -c -g -O2  conftest.c >&5
configure:8450: $? = 0
configure:8465: gcc -shared conftest.o  -v -Wl,-soname -Wl,conftest -o conftest 2\>\&1 \| grep  -lc  \>/dev/null 2\>\&1
configure:8468: $? = 0
configure:8480: result: no
configure:8488: checking dynamic linker characteristics
configure:9064: result: GNU/Linux ld.so
configure:9073: checking how to hardcode library paths into programs
configure:9098: result: immediate
configure:9112: checking whether stripping libraries is possible
configure:9117: result: yes
configure:9918: checking if libtool supports shared libraries
configure:9920: result: yes
configure:9923: checking whether to build shared libraries
configure:9944: result: yes
configure:9947: checking whether to build static libraries
configure:9951: result: yes
configure:10043: creating libtool
configure:10631: checking for ld used by g++
configure:10698: result: /usr/bin/ld -m elf_x86_64
configure:10707: checking if the linker (/usr/bin/ld -m elf_x86_64) is GNU ld
configure:10722: result: yes
configure:10773: checking whether the g++ linker (/usr/bin/ld -m elf_x86_64) supports shared libraries
configure:11707: result: yes
configure:11725: g++ -c -g -O2  conftest.cpp >&5
configure:11728: $? = 0
configure:11847: checking for g++ option to produce PIC
configure:12121: result: -fPIC
configure:12129: checking if g++ PIC flag -fPIC works
configure:12147: g++ -c -g -O2  -fPIC -DPIC conftest.cpp >&5
configure:12151: $? = 0
configure:12164: result: yes
configure:12192: checking if g++ static flag -static works
configure:12220: result: yes
configure:12230: checking if g++ supports -c -o file.o
configure:12251: g++ -c -g -O2  -o out/conftest2.o conftest.cpp >&5
configure:12255: $? = 0
configure:12277: result: yes
configure:12303: checking whether the g++ linker (/usr/bin/ld -m elf_x86_64) supports shared libraries
configure:12331: result: yes
configure:12398: checking dynamic linker characteristics
configure:12974: result: GNU/Linux ld.so
configure:12983: checking how to hardcode library paths into programs
configure:13008: result: immediate
configure:19129: checking for dlopen in -ldl
configure:19164: gcc -o conftest -g -O2   conftest.c -ldl   >&5
configure:19170: $? = 0
configure:19188: result: yes
configure:19200: checking for pthread_create in -lpthread
configure:19235: gcc -o conftest -g -O2   conftest.c -lpthread  -ldl  >&5
configure:19241: $? = 0
configure:19259: result: yes
configure:19272: checking for ANSI C header files
configure:19436: result: yes
configure:19471: checking float.h usability
configure:19488: gcc -c -g -O2  conftest.c >&5
configure:19494: $? = 0
configure:19508: result: yes
configure:19512: checking float.h presence
configure:19527: gcc -E  conftest.c
configure:19533: $? = 0
configure:19547: result: yes
configure:19580: checking for float.h
configure:19588: result: yes
configure:19461: checking for inttypes.h
configure:19467: result: yes
configure:19471: checking limits.h usability
configure:19488: gcc -c -g -O2  conftest.c >&5
configure:19494: $? = 0
configure:19508: result: yes
configure:19512: checking limits.h presence
configure:19527: gcc -E  conftest.c
configure:19533: $? = 0
configure:19547: result: yes
configure:19580: checking for limits.h
configure:19588: result: yes
configure:19471: checking locale.h usability
configure:19488: gcc -c -g -O2  conftest.c >&5
configure:19494: $? = 0
configure:19508: result: yes
configure:19512: checking locale.h presence
configure:19527: gcc -E  conftest.c
configure:19533: $? = 0
configure:19547: result: yes
configure:19580: checking for locale.h
configure:19588: result: yes
configure:19471: checking stddef.h usability
configure:19488: gcc -c -g -O2  conftest.c >&5
configure:19494: $? = 0
configure:19508: result: yes
configure:19512: checking stddef.h presence
configure:19527: gcc -E  conftest.c
configure:19533: $? = 0
configure:19547: result: yes
configure:19580: checking for stddef.h
configure:19588: result: yes
configure:19461: checking for stdint.h
configure:19467: result: yes
configure:19461: checking for stdlib.h
configure:19467: result: yes
configure:19461: checking for string.h
configure:19467: result: yes
configure:19461: checking for unistd.h
configure:19467: result: yes
configure:19471: checking wchar.h usability
configure:19488: gcc -c -g -O2  conftest.c >&5
configure:19494: $? = 0
configure:19508: result: yes
configure:19512: checking wchar.h presence
configure:19527: gcc -E  conftest.c
configure:19533: $? = 0
configure:19547: result: yes
configure:19580: checking for wchar.h
configure:19588: result: yes
configure:19471: checking wctype.h usability
configure:19488: gcc -c -g -O2  conftest.c >&5
configure:19494: $? = 0
configure:19508: result: yes
configure:19512: checking wctype.h presence
configure:19527: gcc -E  conftest.c
configure:19533: $? = 0
configure:19547: result: yes
configure:19580: checking for wctype.h
configure:19588: result: yes
configure:19603: checking for an ANSI C-conforming const
configure:19678: gcc -c -g -O2  conftest.c >&5
configure:19684: $? = 0
configure:19699: result: yes
configure:19709: checking for size_t
configure:19739: gcc -c -g -O2  conftest.c >&5
configure:19745: $? = 0
configure:19760: result: yes
configure:19772: checking whether struct tm is in sys/time.h or time.h
configure:19802: gcc -c -g -O2  conftest.c >&5
configure:19808: $? = 0
configure:19823: result: time.h
configure:19833: checking for working volatile
configure:19862: gcc -c -g -O2  conftest.c >&5
configure:19868: $? = 0
configure:19883: result: yes
configure:19893: checking for ptrdiff_t
configure:19923: gcc -c -g -O2  conftest.c >&5
configure:19929: $? = 0
configure:19944: result: yes
configure:19957: checking return type of signal handlers
configure:19985: gcc -c -g -O2  conftest.c >&5
conftest.c: In function 'main':
conftest.c:42:10: error: void value not ignored as it ought to be
   42 | return *(signal (0, 0)) (0) == 1;
      |         ~^~~~~~~~~~~~~~~~~~
configure:19991: $? = 1
configure: failed program was:
| /* confdefs.h.  */
| #define PACKAGE_NAME "ta-lib"
| #define PACKAGE_TARNAME "ta-lib"
| #define PACKAGE_VERSION "0.4.0"
| #define PACKAGE_STRING "ta-lib 0.4.0"
| #define PACKAGE_BUGREPORT "http://sourceforge.net/tracker/?group_id=8903&atid=108903"
| #define PACKAGE "ta-lib"
| #define VERSION "0.4.0"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DLFCN_H 1
| #define HAVE_LIBDL 1
| #define HAVE_LIBPTHREAD 1
| #define STDC_HEADERS 1
| #define HAVE_FLOAT_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LOCALE_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define HAVE_WCTYPE_H 1
| #define HAVE_PTRDIFF_T 1
| /* end confdefs.h.  */
| #include <sys/types.h>
| #include <signal.h>
| 
| int
| main ()
| {
| return *(signal (0, 0)) (0) == 1;
|   ;
|   return 0;
| }
configure:20006: result: void
configure:20014: checking for working strcoll
configure:20045: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
configure:20048: $? = 0
configure:20054: ./conftest
configure:20057: $? = 0
configure:20073: result: yes
configure:20087: checking for strftime
configure:20143: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:61:6: warning: conflicting types for built-in function 'strftime'; expected 'long unsigned int(char *, long unsigned int,  const char *, const void *)' [-Wbuiltin-declaration-mismatch]
   61 | char strftime ();
      |      ^~~~~~~~
conftest.c:49:1: note: 'strftime' is declared in header '<time.h>'
   48 | # include <limits.h>
   49 | #else
configure:20149: $? = 0
configure:20167: result: yes
configure:20248: checking for working strtod
configure:20299: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
configure:20302: $? = 0
configure:20308: ./conftest
configure:20311: $? = 0
configure:20327: result: yes
configure:20495: checking for vprintf
configure:20551: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:62:6: warning: conflicting types for built-in function 'vprintf'; expected 'int(const char *, __va_list_tag *)' [-Wbuiltin-declaration-mismatch]
   62 | char vprintf ();
      |      ^~~~~~~
conftest.c:50:1: note: 'vprintf' is declared in header '<stdio.h>'
   49 | # include <limits.h>
   50 | #else
configure:20557: $? = 0
configure:20575: result: yes
configure:20582: checking for _doprnt
configure:20638: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
/usr/bin/ld: /tmp/ccOKDI1Q.o: in function `main':
/home/<USER>/Algo-trade/live-data-fetching/ta-lib/conftest.c:74:(.text.startup+0xb): undefined reference to `_doprnt'
collect2: error: ld returned 1 exit status
configure:20644: $? = 1
configure: failed program was:
| /* confdefs.h.  */
| #define PACKAGE_NAME "ta-lib"
| #define PACKAGE_TARNAME "ta-lib"
| #define PACKAGE_VERSION "0.4.0"
| #define PACKAGE_STRING "ta-lib 0.4.0"
| #define PACKAGE_BUGREPORT "http://sourceforge.net/tracker/?group_id=8903&atid=108903"
| #define PACKAGE "ta-lib"
| #define VERSION "0.4.0"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DLFCN_H 1
| #define HAVE_LIBDL 1
| #define HAVE_LIBPTHREAD 1
| #define STDC_HEADERS 1
| #define HAVE_FLOAT_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LOCALE_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define HAVE_WCTYPE_H 1
| #define HAVE_PTRDIFF_T 1
| #define RETSIGTYPE void
| #define HAVE_STRCOLL 1
| #define HAVE_STRFTIME 1
| #define HAVE_VPRINTF 1
| /* end confdefs.h.  */
| /* Define _doprnt to an innocuous variant, in case <limits.h> declares _doprnt.
|    For example, HP-UX 11i <limits.h> declares gettimeofday.  */
| #define _doprnt innocuous__doprnt
| 
| /* System header to define __stub macros and hopefully few prototypes,
|     which can conflict with char _doprnt (); below.
|     Prefer <limits.h> to <assert.h> if __STDC__ is defined, since
|     <limits.h> exists even on freestanding compilers.  */
| 
| #ifdef __STDC__
| # include <limits.h>
| #else
| # include <assert.h>
| #endif
| 
| #undef _doprnt
| 
| /* Override any GCC internal prototype to avoid an error.
|    Use char because int might match the return type of a GCC
|    builtin and then its argument prototype would still apply.  */
| #ifdef __cplusplus
| extern "C"
| #endif
| char _doprnt ();
| /* The GNU C library defines this for functions which it implements
|     to always fail with ENOSYS.  Some functions are actually named
|     something starting with __ and the normal name is an alias.  */
| #if defined __stub__doprnt || defined __stub____doprnt
| choke me
| #endif
| 
| int
| main ()
| {
| return _doprnt ();
|   ;
|   return 0;
| }
configure:20661: result: no
configure:20695: checking for floor
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:63:6: warning: conflicting types for built-in function 'floor'; expected 'double(double)' [-Wbuiltin-declaration-mismatch]
   63 | char floor ();
      |      ^~~~~
conftest.c:51:1: note: 'floor' is declared in header '<math.h>'
   50 | # include <limits.h>
   51 | #else
/usr/bin/ld: /tmp/ccagEYhE.o: in function `main':
/home/<USER>/Algo-trade/live-data-fetching/ta-lib/conftest.c:74:(.text.startup+0xb): undefined reference to `floor'
collect2: error: ld returned 1 exit status
configure:20757: $? = 1
configure: failed program was:
| /* confdefs.h.  */
| #define PACKAGE_NAME "ta-lib"
| #define PACKAGE_TARNAME "ta-lib"
| #define PACKAGE_VERSION "0.4.0"
| #define PACKAGE_STRING "ta-lib 0.4.0"
| #define PACKAGE_BUGREPORT "http://sourceforge.net/tracker/?group_id=8903&atid=108903"
| #define PACKAGE "ta-lib"
| #define VERSION "0.4.0"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DLFCN_H 1
| #define HAVE_LIBDL 1
| #define HAVE_LIBPTHREAD 1
| #define STDC_HEADERS 1
| #define HAVE_FLOAT_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LOCALE_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define HAVE_WCTYPE_H 1
| #define HAVE_PTRDIFF_T 1
| #define RETSIGTYPE void
| #define HAVE_STRCOLL 1
| #define HAVE_STRFTIME 1
| #define HAVE_VPRINTF 1
| /* end confdefs.h.  */
| /* Define floor to an innocuous variant, in case <limits.h> declares floor.
|    For example, HP-UX 11i <limits.h> declares gettimeofday.  */
| #define floor innocuous_floor
| 
| /* System header to define __stub macros and hopefully few prototypes,
|     which can conflict with char floor (); below.
|     Prefer <limits.h> to <assert.h> if __STDC__ is defined, since
|     <limits.h> exists even on freestanding compilers.  */
| 
| #ifdef __STDC__
| # include <limits.h>
| #else
| # include <assert.h>
| #endif
| 
| #undef floor
| 
| /* Override any GCC internal prototype to avoid an error.
|    Use char because int might match the return type of a GCC
|    builtin and then its argument prototype would still apply.  */
| #ifdef __cplusplus
| extern "C"
| #endif
| char floor ();
| /* The GNU C library defines this for functions which it implements
|     to always fail with ENOSYS.  Some functions are actually named
|     something starting with __ and the normal name is an alias.  */
| #if defined __stub_floor || defined __stub___floor
| choke me
| #endif
| 
| int
| main ()
| {
| return floor ();
|   ;
|   return 0;
| }
configure:20775: result: no
configure:20695: checking for isascii
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:63:6: warning: conflicting types for built-in function 'isascii'; expected 'int(int)' [-Wbuiltin-declaration-mismatch]
   63 | char isascii ();
      |      ^~~~~~~
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for localeconv
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for mblen
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for memmove
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:66:6: warning: conflicting types for built-in function 'memmove'; expected 'void *(void *, const void *, long unsigned int)' [-Wbuiltin-declaration-mismatch]
   66 | char memmove ();
      |      ^~~~~~~
conftest.c:54:1: note: 'memmove' is declared in header '<string.h>'
   53 | # include <limits.h>
   54 | #else
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for memset
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:67:6: warning: conflicting types for built-in function 'memset'; expected 'void *(void *, int,  long unsigned int)' [-Wbuiltin-declaration-mismatch]
   67 | char memset ();
      |      ^~~~~~
conftest.c:55:1: note: 'memset' is declared in header '<string.h>'
   54 | # include <limits.h>
   55 | #else
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for modf
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:68:6: warning: conflicting types for built-in function 'modf'; expected 'double(double,  double *)' [-Wbuiltin-declaration-mismatch]
   68 | char modf ();
      |      ^~~~
conftest.c:56:1: note: 'modf' is declared in header '<math.h>'
   55 | # include <limits.h>
   56 | #else
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for pow
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:69:6: warning: conflicting types for built-in function 'pow'; expected 'double(double,  double)' [-Wbuiltin-declaration-mismatch]
   69 | char pow ();
      |      ^~~
conftest.c:57:1: note: 'pow' is declared in header '<math.h>'
   56 | # include <limits.h>
   57 | #else
/usr/bin/ld: /tmp/ccMw49ZC.o: in function `main':
/home/<USER>/Algo-trade/live-data-fetching/ta-lib/conftest.c:80:(.text.startup+0xb): undefined reference to `pow'
collect2: error: ld returned 1 exit status
configure:20757: $? = 1
configure: failed program was:
| /* confdefs.h.  */
| #define PACKAGE_NAME "ta-lib"
| #define PACKAGE_TARNAME "ta-lib"
| #define PACKAGE_VERSION "0.4.0"
| #define PACKAGE_STRING "ta-lib 0.4.0"
| #define PACKAGE_BUGREPORT "http://sourceforge.net/tracker/?group_id=8903&atid=108903"
| #define PACKAGE "ta-lib"
| #define VERSION "0.4.0"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DLFCN_H 1
| #define HAVE_LIBDL 1
| #define HAVE_LIBPTHREAD 1
| #define STDC_HEADERS 1
| #define HAVE_FLOAT_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LOCALE_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define HAVE_WCTYPE_H 1
| #define HAVE_PTRDIFF_T 1
| #define RETSIGTYPE void
| #define HAVE_STRCOLL 1
| #define HAVE_STRFTIME 1
| #define HAVE_VPRINTF 1
| #define HAVE_ISASCII 1
| #define HAVE_LOCALECONV 1
| #define HAVE_MBLEN 1
| #define HAVE_MEMMOVE 1
| #define HAVE_MEMSET 1
| #define HAVE_MODF 1
| /* end confdefs.h.  */
| /* Define pow to an innocuous variant, in case <limits.h> declares pow.
|    For example, HP-UX 11i <limits.h> declares gettimeofday.  */
| #define pow innocuous_pow
| 
| /* System header to define __stub macros and hopefully few prototypes,
|     which can conflict with char pow (); below.
|     Prefer <limits.h> to <assert.h> if __STDC__ is defined, since
|     <limits.h> exists even on freestanding compilers.  */
| 
| #ifdef __STDC__
| # include <limits.h>
| #else
| # include <assert.h>
| #endif
| 
| #undef pow
| 
| /* Override any GCC internal prototype to avoid an error.
|    Use char because int might match the return type of a GCC
|    builtin and then its argument prototype would still apply.  */
| #ifdef __cplusplus
| extern "C"
| #endif
| char pow ();
| /* The GNU C library defines this for functions which it implements
|     to always fail with ENOSYS.  Some functions are actually named
|     something starting with __ and the normal name is an alias.  */
| #if defined __stub_pow || defined __stub___pow
| choke me
| #endif
| 
| int
| main ()
| {
| return pow ();
|   ;
|   return 0;
| }
configure:20775: result: no
configure:20695: checking for sqrt
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:69:6: warning: conflicting types for built-in function 'sqrt'; expected 'double(double)' [-Wbuiltin-declaration-mismatch]
   69 | char sqrt ();
      |      ^~~~
conftest.c:57:1: note: 'sqrt' is declared in header '<math.h>'
   56 | # include <limits.h>
   57 | #else
/usr/bin/ld: /tmp/ccweW36G.o: in function `main':
/home/<USER>/Algo-trade/live-data-fetching/ta-lib/conftest.c:80:(.text.startup+0xb): undefined reference to `sqrt'
collect2: error: ld returned 1 exit status
configure:20757: $? = 1
configure: failed program was:
| /* confdefs.h.  */
| #define PACKAGE_NAME "ta-lib"
| #define PACKAGE_TARNAME "ta-lib"
| #define PACKAGE_VERSION "0.4.0"
| #define PACKAGE_STRING "ta-lib 0.4.0"
| #define PACKAGE_BUGREPORT "http://sourceforge.net/tracker/?group_id=8903&atid=108903"
| #define PACKAGE "ta-lib"
| #define VERSION "0.4.0"
| #define STDC_HEADERS 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_MEMORY_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_DLFCN_H 1
| #define HAVE_LIBDL 1
| #define HAVE_LIBPTHREAD 1
| #define STDC_HEADERS 1
| #define HAVE_FLOAT_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_LIMITS_H 1
| #define HAVE_LOCALE_H 1
| #define HAVE_STDDEF_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_UNISTD_H 1
| #define HAVE_WCHAR_H 1
| #define HAVE_WCTYPE_H 1
| #define HAVE_PTRDIFF_T 1
| #define RETSIGTYPE void
| #define HAVE_STRCOLL 1
| #define HAVE_STRFTIME 1
| #define HAVE_VPRINTF 1
| #define HAVE_ISASCII 1
| #define HAVE_LOCALECONV 1
| #define HAVE_MBLEN 1
| #define HAVE_MEMMOVE 1
| #define HAVE_MEMSET 1
| #define HAVE_MODF 1
| /* end confdefs.h.  */
| /* Define sqrt to an innocuous variant, in case <limits.h> declares sqrt.
|    For example, HP-UX 11i <limits.h> declares gettimeofday.  */
| #define sqrt innocuous_sqrt
| 
| /* System header to define __stub macros and hopefully few prototypes,
|     which can conflict with char sqrt (); below.
|     Prefer <limits.h> to <assert.h> if __STDC__ is defined, since
|     <limits.h> exists even on freestanding compilers.  */
| 
| #ifdef __STDC__
| # include <limits.h>
| #else
| # include <assert.h>
| #endif
| 
| #undef sqrt
| 
| /* Override any GCC internal prototype to avoid an error.
|    Use char because int might match the return type of a GCC
|    builtin and then its argument prototype would still apply.  */
| #ifdef __cplusplus
| extern "C"
| #endif
| char sqrt ();
| /* The GNU C library defines this for functions which it implements
|     to always fail with ENOSYS.  Some functions are actually named
|     something starting with __ and the normal name is an alias.  */
| #if defined __stub_sqrt || defined __stub___sqrt
| choke me
| #endif
| 
| int
| main ()
| {
| return sqrt ();
|   ;
|   return 0;
| }
configure:20775: result: no
configure:20695: checking for strcasecmp
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:69:6: warning: conflicting types for built-in function 'strcasecmp'; expected 'int(const char *, const char *)' [-Wbuiltin-declaration-mismatch]
   69 | char strcasecmp ();
      |      ^~~~~~~~~~
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for strchr
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:70:6: warning: conflicting types for built-in function 'strchr'; expected 'char *(const char *, int)' [-Wbuiltin-declaration-mismatch]
   70 | char strchr ();
      |      ^~~~~~
conftest.c:58:1: note: 'strchr' is declared in header '<string.h>'
   57 | # include <limits.h>
   58 | #else
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for strerror
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for strncasecmp
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:72:6: warning: conflicting types for built-in function 'strncasecmp'; expected 'int(const char *, const char *, long unsigned int)' [-Wbuiltin-declaration-mismatch]
   72 | char strncasecmp ();
      |      ^~~~~~~~~~~
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for strrchr
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:73:6: warning: conflicting types for built-in function 'strrchr'; expected 'char *(const char *, int)' [-Wbuiltin-declaration-mismatch]
   73 | char strrchr ();
      |      ^~~~~~~
conftest.c:61:1: note: 'strrchr' is declared in header '<string.h>'
   60 | # include <limits.h>
   61 | #else
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for strstr
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
conftest.c:74:6: warning: conflicting types for built-in function 'strstr'; expected 'char *(const char *, const char *)' [-Wbuiltin-declaration-mismatch]
   74 | char strstr ();
      |      ^~~~~~
conftest.c:62:1: note: 'strstr' is declared in header '<string.h>'
   61 | # include <limits.h>
   62 | #else
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for strtol
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
configure:20757: $? = 0
configure:20775: result: yes
configure:20695: checking for strtoul
configure:20751: gcc -o conftest -g -O2   conftest.c -lpthread -ldl  >&5
configure:20757: $? = 0
configure:20775: result: yes
configure:20909: creating ./config.status

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by ta-lib config.status 0.4.0, which was
generated by GNU Autoconf 2.61.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status 

on raghunandansrinivas-HP-ZBook-15-G5

config.status:673: creating Makefile
config.status:673: creating src/Makefile
config.status:673: creating src/ta_abstract/Makefile
config.status:673: creating src/ta_common/Makefile
config.status:673: creating src/ta_func/Makefile
config.status:673: creating src/tools/Makefile
config.status:673: creating src/tools/gen_code/Makefile
config.status:673: creating src/tools/ta_regtest/Makefile
config.status:673: creating ta-lib-config
config.status:673: creating ta-lib.spec
config.status:673: creating ta-lib.dpkg
config.status:673: creating include/ta_config.h
config.status:987: executing depfiles commands

## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_build=x86_64-unknown-linux-gnu
ac_cv_c_compiler_gnu=yes
ac_cv_c_const=yes
ac_cv_c_volatile=yes
ac_cv_cxx_compiler_gnu=yes
ac_cv_env_CCC_set=
ac_cv_env_CCC_value=
ac_cv_env_CC_set=
ac_cv_env_CC_value=
ac_cv_env_CFLAGS_set=
ac_cv_env_CFLAGS_value=
ac_cv_env_CPPFLAGS_set=
ac_cv_env_CPPFLAGS_value=
ac_cv_env_CPP_set=
ac_cv_env_CPP_value=
ac_cv_env_CXXCPP_set=
ac_cv_env_CXXCPP_value=
ac_cv_env_CXXFLAGS_set=
ac_cv_env_CXXFLAGS_value=
ac_cv_env_CXX_set=
ac_cv_env_CXX_value=
ac_cv_env_F77_set=
ac_cv_env_F77_value=
ac_cv_env_FFLAGS_set=
ac_cv_env_FFLAGS_value=
ac_cv_env_LDFLAGS_set=
ac_cv_env_LDFLAGS_value=
ac_cv_env_LIBS_set=
ac_cv_env_LIBS_value=
ac_cv_env_build_alias_set=
ac_cv_env_build_alias_value=
ac_cv_env_host_alias_set=
ac_cv_env_host_alias_value=
ac_cv_env_target_alias_set=
ac_cv_env_target_alias_value=
ac_cv_f77_compiler_gnu=no
ac_cv_func__doprnt=no
ac_cv_func_floor=no
ac_cv_func_isascii=yes
ac_cv_func_localeconv=yes
ac_cv_func_mblen=yes
ac_cv_func_memmove=yes
ac_cv_func_memset=yes
ac_cv_func_modf=yes
ac_cv_func_pow=no
ac_cv_func_sqrt=no
ac_cv_func_strcasecmp=yes
ac_cv_func_strchr=yes
ac_cv_func_strcoll_works=yes
ac_cv_func_strerror=yes
ac_cv_func_strftime=yes
ac_cv_func_strncasecmp=yes
ac_cv_func_strrchr=yes
ac_cv_func_strstr=yes
ac_cv_func_strtod=yes
ac_cv_func_strtol=yes
ac_cv_func_strtoul=yes
ac_cv_func_vprintf=yes
ac_cv_header_dlfcn_h=yes
ac_cv_header_float_h=yes
ac_cv_header_inttypes_h=yes
ac_cv_header_limits_h=yes
ac_cv_header_locale_h=yes
ac_cv_header_memory_h=yes
ac_cv_header_stdc=yes
ac_cv_header_stddef_h=yes
ac_cv_header_stdint_h=yes
ac_cv_header_stdlib_h=yes
ac_cv_header_string_h=yes
ac_cv_header_strings_h=yes
ac_cv_header_sys_stat_h=yes
ac_cv_header_sys_types_h=yes
ac_cv_header_unistd_h=yes
ac_cv_header_wchar_h=yes
ac_cv_header_wctype_h=yes
ac_cv_host=x86_64-unknown-linux-gnu
ac_cv_lib_dl_dlopen=yes
ac_cv_lib_pthread_pthread_create=yes
ac_cv_objext=o
ac_cv_path_EGREP='/usr/bin/grep -E'
ac_cv_path_GREP=/usr/bin/grep
ac_cv_path_install='/usr/bin/install -c'
ac_cv_path_mkdir=/usr/bin/mkdir
ac_cv_prog_AWK=mawk
ac_cv_prog_CPP='gcc -E'
ac_cv_prog_CXXCPP='g++ -E'
ac_cv_prog_ac_ct_AR=ar
ac_cv_prog_ac_ct_CC=gcc
ac_cv_prog_ac_ct_CXX=g++
ac_cv_prog_ac_ct_RANLIB=ranlib
ac_cv_prog_ac_ct_STRIP=strip
ac_cv_prog_cc_c89=
ac_cv_prog_cc_g=yes
ac_cv_prog_cxx_g=yes
ac_cv_prog_f77_g=no
ac_cv_prog_make_make_set=yes
ac_cv_struct_tm=time.h
ac_cv_type_ptrdiff_t=yes
ac_cv_type_signal=void
ac_cv_type_size_t=yes
am_cv_CC_dependencies_compiler_type=gcc3
am_cv_CXX_dependencies_compiler_type=gcc3
lt_cv_deplibs_check_method=pass_all
lt_cv_file_magic_cmd='$MAGIC_CMD'
lt_cv_file_magic_test_file=
lt_cv_ld_reload_flag=-r
lt_cv_objdir=.libs
lt_cv_path_LD=/usr/bin/ld
lt_cv_path_LDCXX='/usr/bin/ld -m elf_x86_64'
lt_cv_path_NM='/usr/bin/nm -B'
lt_cv_path_SED=/usr/bin/sed
lt_cv_prog_compiler_c_o=yes
lt_cv_prog_compiler_c_o_CXX=yes
lt_cv_prog_compiler_rtti_exceptions=no
lt_cv_prog_gnu_ld=yes
lt_cv_prog_gnu_ldcxx=yes
lt_cv_sys_global_symbol_pipe='sed -n -e '\''s/^.*[ 	]\([ABCDGIRSTW][ABCDGIRSTW]*\)[ 	][ 	]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p'\'''
lt_cv_sys_global_symbol_to_c_name_address='sed -n -e '\''s/^: \([^ ]*\) $/  {\"\1\", (lt_ptr) 0},/p'\'' -e '\''s/^[BCDEGRST] \([^ ]*\) \([^ ]*\)$/  {"\2", (lt_ptr) \&\2},/p'\'''
lt_cv_sys_global_symbol_to_cdecl='sed -n -e '\''s/^. .* \(.*\)$/extern int \1;/p'\'''
lt_cv_sys_max_cmd_len=32768
lt_lt_cv_prog_compiler_c_o='"yes"'
lt_lt_cv_prog_compiler_c_o_CXX='"yes"'
lt_lt_cv_sys_global_symbol_pipe='"sed -n -e '\''s/^.*[ 	]\\([ABCDGIRSTW][ABCDGIRSTW]*\\)[ 	][ 	]*\\([_A-Za-z][_A-Za-z0-9]*\\)\$/\\1 \\2 \\2/p'\''"'
lt_lt_cv_sys_global_symbol_to_c_name_address='"sed -n -e '\''s/^: \\([^ ]*\\) \$/  {\\\"\\1\\\", (lt_ptr) 0},/p'\'' -e '\''s/^[BCDEGRST] \\([^ ]*\\) \\([^ ]*\\)\$/  {\"\\2\", (lt_ptr) \\&\\2},/p'\''"'
lt_lt_cv_sys_global_symbol_to_cdecl='"sed -n -e '\''s/^. .* \\(.*\\)\$/extern int \\1;/p'\''"'

## ----------------- ##
## Output variables. ##
## ----------------- ##

ACLOCAL='${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run aclocal-1.10'
AMDEPBACKSLASH='\'
AMDEP_FALSE='#'
AMDEP_TRUE=''
AMTAR='${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run tar'
AR='ar'
AUTOCONF='${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run autoconf'
AUTOHEADER='${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run autoheader'
AUTOMAKE='${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run automake-1.10'
AWK='mawk'
CC='gcc'
CCDEPMODE='depmode=gcc3'
CFLAGS='-g -O2'
CPP='gcc -E'
CPPFLAGS=''
CXX='g++'
CXXCPP='g++ -E'
CXXDEPMODE='depmode=gcc3'
CXXFLAGS='-g -O2'
CYGPATH_W='echo'
DEFS='-DHAVE_CONFIG_H'
DEPDIR='.deps'
ECHO='echo'
ECHO_C=''
ECHO_N='-n'
ECHO_T=''
EGREP='/usr/bin/grep -E'
EXEEXT=''
F77=''
FFLAGS=''
GREP='/usr/bin/grep'
INSTALL_DATA='${INSTALL} -m 644'
INSTALL_PROGRAM='${INSTALL}'
INSTALL_SCRIPT='${INSTALL}'
INSTALL_STRIP_PROGRAM='$(install_sh) -c -s'
LDFLAGS=''
LIBOBJS=''
LIBS='-lpthread -ldl '
LIBTOOL='$(SHELL) $(top_builddir)/libtool'
LN_S='ln -s'
LTLIBOBJS=''
MAKEINFO='${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run makeinfo'
OBJEXT='o'
PACKAGE='ta-lib'
PACKAGE_BUGREPORT='http://sourceforge.net/tracker/?group_id=8903&atid=108903'
PACKAGE_NAME='ta-lib'
PACKAGE_STRING='ta-lib 0.4.0'
PACKAGE_TARNAME='ta-lib'
PACKAGE_VERSION='0.4.0'
PATH_SEPARATOR=':'
POW_LIB=''
RANLIB='ranlib'
SET_MAKE=''
SHELL='/bin/bash'
STRIP='strip'
TALIB_LIBRARY_VERSION='0:0:0'
VERSION='0.4.0'
ac_ct_CC='gcc'
ac_ct_CXX='g++'
ac_ct_F77=''
am__fastdepCC_FALSE='#'
am__fastdepCC_TRUE=''
am__fastdepCXX_FALSE='#'
am__fastdepCXX_TRUE=''
am__include='include'
am__isrc=''
am__leading_dot='.'
am__quote=''
am__tar='${AMTAR} chof - "$$tardir"'
am__untar='${AMTAR} xf -'
bindir='${exec_prefix}/bin'
build='x86_64-unknown-linux-gnu'
build_alias=''
build_cpu='x86_64'
build_os='linux-gnu'
build_vendor='unknown'
datadir='${datarootdir}'
datarootdir='${prefix}/share'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
dvidir='${docdir}'
exec_prefix='${prefix}'
host='x86_64-unknown-linux-gnu'
host_alias=''
host_cpu='x86_64'
host_os='linux-gnu'
host_vendor='unknown'
htmldir='${docdir}'
includedir='${prefix}/include'
infodir='${datarootdir}/info'
install_sh='$(SHELL) /home/<USER>/Algo-trade/live-data-fetching/ta-lib/install-sh'
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
localedir='${datarootdir}/locale'
localstatedir='${prefix}/var'
mandir='${datarootdir}/man'
mkdir_p='/usr/bin/mkdir -p'
oldincludedir='/usr/include'
pdfdir='${docdir}'
prefix='/usr/local'
program_transform_name='s,x,x,'
psdir='${docdir}'
sbindir='${exec_prefix}/sbin'
sharedstatedir='${prefix}/com'
sysconfdir='${prefix}/etc'
target_alias=''

## ----------- ##
## confdefs.h. ##
## ----------- ##

#define PACKAGE_NAME "ta-lib"
#define PACKAGE_TARNAME "ta-lib"
#define PACKAGE_VERSION "0.4.0"
#define PACKAGE_STRING "ta-lib 0.4.0"
#define PACKAGE_BUGREPORT "http://sourceforge.net/tracker/?group_id=8903&atid=108903"
#define PACKAGE "ta-lib"
#define VERSION "0.4.0"
#define STDC_HEADERS 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_MEMORY_H 1
#define HAVE_STRINGS_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_STDINT_H 1
#define HAVE_UNISTD_H 1
#define HAVE_DLFCN_H 1
#define HAVE_LIBDL 1
#define HAVE_LIBPTHREAD 1
#define STDC_HEADERS 1
#define HAVE_FLOAT_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_LIMITS_H 1
#define HAVE_LOCALE_H 1
#define HAVE_STDDEF_H 1
#define HAVE_STDINT_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_UNISTD_H 1
#define HAVE_WCHAR_H 1
#define HAVE_WCTYPE_H 1
#define HAVE_PTRDIFF_T 1
#define RETSIGTYPE void
#define HAVE_STRCOLL 1
#define HAVE_STRFTIME 1
#define HAVE_VPRINTF 1
#define HAVE_ISASCII 1
#define HAVE_LOCALECONV 1
#define HAVE_MBLEN 1
#define HAVE_MEMMOVE 1
#define HAVE_MEMSET 1
#define HAVE_MODF 1
#define HAVE_STRCASECMP 1
#define HAVE_STRCHR 1
#define HAVE_STRERROR 1
#define HAVE_STRNCASECMP 1
#define HAVE_STRRCHR 1
#define HAVE_STRSTR 1
#define HAVE_STRTOL 1
#define HAVE_STRTOUL 1

configure: exit 0
