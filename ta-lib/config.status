#! /bin/bash
# Generated by configure.
# Run this file to recreate the current configuration.
# Compiler output produced by configure, useful for debugging
# configure, is in config.log if it exists.

debug=false
ac_cs_recheck=false
ac_cs_silent=false
SHELL=${CONFIG_SHELL-/bin/bash}
## --------------------- ##
## M4sh Initialization.  ##
## --------------------- ##

# Be more Bourne compatible
DUALCASE=1; export DUALCASE # for MKS sh
if test -n "${ZSH_VERSION+set}" && (emulate sh) >/dev/null 2>&1; then
  emulate sh
  NULLCMD=:
  # Zsh 3.x and 4.x performs word splitting on ${1+"$@"}, which
  # is contrary to our usage.  Disable this feature.
  alias -g '${1+"$@"}'='"$@"'
  setopt NO_GLOB_SUBST
else
  case `(set -o) 2>/dev/null` in
  *posix*) set -o posix ;;
esac

fi




# PATH needs CR
# Avoid depending upon Character Ranges.
as_cr_letters='abcdefghijklmnopqrstuvwxyz'
as_cr_LETTERS='ABCDEFGHIJKLMNOPQRSTUVWXYZ'
as_cr_Letters=$as_cr_letters$as_cr_LETTERS
as_cr_digits='0123456789'
as_cr_alnum=$as_cr_Letters$as_cr_digits

# The user is always right.
if test "${PATH_SEPARATOR+set}" != set; then
  echo "#! /bin/sh" >conf$$.sh
  echo  "exit 0"   >>conf$$.sh
  chmod +x conf$$.sh
  if (PATH="/nonexistent;."; conf$$.sh) >/dev/null 2>&1; then
    PATH_SEPARATOR=';'
  else
    PATH_SEPARATOR=:
  fi
  rm -f conf$$.sh
fi

# Support unset when possible.
if ( (MAIL=60; unset MAIL) || exit) >/dev/null 2>&1; then
  as_unset=unset
else
  as_unset=false
fi


# IFS
# We need space, tab and new line, in precisely that order.  Quoting is
# there to prevent editors from complaining about space-tab.
# (If _AS_PATH_WALK were called with IFS unset, it would disable word
# splitting by setting IFS to empty value.)
as_nl='
'
IFS=" ""	$as_nl"

# Find who we are.  Look in the path if we contain no directory separator.
case $0 in
  *[\\/]* ) as_myself=$0 ;;
  *) as_save_IFS=$IFS; IFS=$PATH_SEPARATOR
for as_dir in $PATH
do
  IFS=$as_save_IFS
  test -z "$as_dir" && as_dir=.
  test -r "$as_dir/$0" && as_myself=$as_dir/$0 && break
done
IFS=$as_save_IFS

     ;;
esac
# We did not find ourselves, most probably we were run as `sh COMMAND'
# in which case we are not to be found in the path.
if test "x$as_myself" = x; then
  as_myself=$0
fi
if test ! -f "$as_myself"; then
  echo "$as_myself: error: cannot find myself; rerun with an absolute file name" >&2
  { (exit 1); exit 1; }
fi

# Work around bugs in pre-3.0 UWIN ksh.
for as_var in ENV MAIL MAILPATH
do ($as_unset $as_var) >/dev/null 2>&1 && $as_unset $as_var
done
PS1='$ '
PS2='> '
PS4='+ '

# NLS nuisances.
for as_var in \
  LANG LANGUAGE LC_ADDRESS LC_ALL LC_COLLATE LC_CTYPE LC_IDENTIFICATION \
  LC_MEASUREMENT LC_MESSAGES LC_MONETARY LC_NAME LC_NUMERIC LC_PAPER \
  LC_TELEPHONE LC_TIME
do
  if (set +x; test -z "`(eval $as_var=C; export $as_var) 2>&1`"); then
    eval $as_var=C; export $as_var
  else
    ($as_unset $as_var) >/dev/null 2>&1 && $as_unset $as_var
  fi
done

# Required to use basename.
if expr a : '\(a\)' >/dev/null 2>&1 &&
   test "X`expr 00001 : '.*\(...\)'`" = X001; then
  as_expr=expr
else
  as_expr=false
fi

if (basename -- /) >/dev/null 2>&1 && test "X`basename -- / 2>&1`" = "X/"; then
  as_basename=basename
else
  as_basename=false
fi


# Name of the executable.
as_me=`$as_basename -- "$0" ||
$as_expr X/"$0" : '.*/\([^/][^/]*\)/*$' \| \
	 X"$0" : 'X\(//\)$' \| \
	 X"$0" : 'X\(/\)' \| . 2>/dev/null ||
echo X/"$0" |
    sed '/^.*\/\([^/][^/]*\)\/*$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\/\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`

# CDPATH.
$as_unset CDPATH



  as_lineno_1=$LINENO
  as_lineno_2=$LINENO
  test "x$as_lineno_1" != "x$as_lineno_2" &&
  test "x`expr $as_lineno_1 + 1`" = "x$as_lineno_2" || {

  # Create $as_me.lineno as a copy of $as_myself, but with $LINENO
  # uniformly replaced by the line number.  The first 'sed' inserts a
  # line-number line after each line using $LINENO; the second 'sed'
  # does the real work.  The second script uses 'N' to pair each
  # line-number line with the line containing $LINENO, and appends
  # trailing '-' during substitution so that $LINENO is not a special
  # case at line end.
  # (Raja R Harinath suggested sed '=', and Paul Eggert wrote the
  # scripts with optimization help from Paolo Bonzini.  Blame Lee
  # E. McMahon (1931-1989) for sed's syntax.  :-)
  sed -n '
    p
    /[$]LINENO/=
  ' <$as_myself |
    sed '
      s/[$]LINENO.*/&-/
      t lineno
      b
      :lineno
      N
      :loop
      s/[$]LINENO\([^'$as_cr_alnum'_].*\n\)\(.*\)/\2\1\2/
      t loop
      s/-\n.*//
    ' >$as_me.lineno &&
  chmod +x "$as_me.lineno" ||
    { echo "$as_me: error: cannot create $as_me.lineno; rerun with a POSIX shell" >&2
   { (exit 1); exit 1; }; }

  # Don't try to exec as it changes $[0], causing all sort of problems
  # (the dirname of $[0] is not the place where we might find the
  # original and so on.  Autoconf is especially sensitive to this).
  . "./$as_me.lineno"
  # Exit status is that of the last command.
  exit
}


if (as_dir=`dirname -- /` && test "X$as_dir" = X/) >/dev/null 2>&1; then
  as_dirname=dirname
else
  as_dirname=false
fi

ECHO_C= ECHO_N= ECHO_T=
case `echo -n x` in
-n*)
  case `echo 'x\c'` in
  *c*) ECHO_T='	';;	# ECHO_T is single tab character.
  *)   ECHO_C='\c';;
  esac;;
*)
  ECHO_N='-n';;
esac

if expr a : '\(a\)' >/dev/null 2>&1 &&
   test "X`expr 00001 : '.*\(...\)'`" = X001; then
  as_expr=expr
else
  as_expr=false
fi

rm -f conf$$ conf$$.exe conf$$.file
if test -d conf$$.dir; then
  rm -f conf$$.dir/conf$$.file
else
  rm -f conf$$.dir
  mkdir conf$$.dir
fi
echo >conf$$.file
if ln -s conf$$.file conf$$ 2>/dev/null; then
  as_ln_s='ln -s'
  # ... but there are two gotchas:
  # 1) On MSYS, both `ln -s file dir' and `ln file dir' fail.
  # 2) DJGPP < 2.04 has no symlinks; `ln -s' creates a wrapper executable.
  # In both cases, we have to default to `cp -p'.
  ln -s conf$$.file conf$$.dir 2>/dev/null && test ! -f conf$$.exe ||
    as_ln_s='cp -p'
elif ln conf$$.file conf$$ 2>/dev/null; then
  as_ln_s=ln
else
  as_ln_s='cp -p'
fi
rm -f conf$$ conf$$.exe conf$$.dir/conf$$.file conf$$.file
rmdir conf$$.dir 2>/dev/null

if mkdir -p . 2>/dev/null; then
  as_mkdir_p=:
else
  test -d ./-p && rmdir ./-p
  as_mkdir_p=false
fi

if test -x / >/dev/null 2>&1; then
  as_test_x='test -x'
else
  if ls -dL / >/dev/null 2>&1; then
    as_ls_L_option=L
  else
    as_ls_L_option=
  fi
  as_test_x='
    eval sh -c '\''
      if test -d "$1"; then
        test -d "$1/.";
      else
	case $1 in
        -*)set "./$1";;
	esac;
	case `ls -ld'$as_ls_L_option' "$1" 2>/dev/null` in
	???[sx]*):;;*)false;;esac;fi
    '\'' sh
  '
fi
as_executable_p=$as_test_x

# Sed expression to map a string onto a valid CPP name.
as_tr_cpp="eval sed 'y%*$as_cr_letters%P$as_cr_LETTERS%;s%[^_$as_cr_alnum]%_%g'"

# Sed expression to map a string onto a valid variable name.
as_tr_sh="eval sed 'y%*+%pp%;s%[^_$as_cr_alnum]%_%g'"


exec 6>&1

# Save the log message, to keep $[0] and so on meaningful, and to
# report actual input values of CONFIG_FILES etc. instead of their
# values after options handling.
ac_log="
This file was extended by ta-lib $as_me 0.4.0, which was
generated by GNU Autoconf 2.61.  Invocation command line was

  CONFIG_FILES    = $CONFIG_FILES
  CONFIG_HEADERS  = $CONFIG_HEADERS
  CONFIG_LINKS    = $CONFIG_LINKS
  CONFIG_COMMANDS = $CONFIG_COMMANDS
  $ $0 $@

on `(hostname || uname -n) 2>/dev/null | sed 1q`
"

# Files that config.status was made for.
config_files=" Makefile src/Makefile src/ta_abstract/Makefile src/ta_common/Makefile src/ta_func/Makefile src/tools/Makefile src/tools/gen_code/Makefile src/tools/ta_regtest/Makefile ta-lib-config ta-lib.spec ta-lib.dpkg"
config_headers=" include/ta_config.h"
config_commands=" depfiles"

ac_cs_usage="\
\`$as_me' instantiates files from templates according to the
current configuration.

Usage: $0 [OPTIONS] [FILE]...

  -h, --help       print this help, then exit
  -V, --version    print version number and configuration settings, then exit
  -q, --quiet      do not print progress messages
  -d, --debug      don't remove temporary files
      --recheck    update $as_me by reconfiguring in the same conditions
  --file=FILE[:TEMPLATE]
		   instantiate the configuration file FILE
  --header=FILE[:TEMPLATE]
		   instantiate the configuration header FILE

Configuration files:
$config_files

Configuration headers:
$config_headers

Configuration commands:
$config_commands

Report bugs to <<EMAIL>>."

ac_cs_version="\
ta-lib config.status 0.4.0
configured by ./configure, generated by GNU Autoconf 2.61,
  with options \"'--prefix=/usr/local'\"

Copyright (C) 2006 Free Software Foundation, Inc.
This config.status script is free software; the Free Software Foundation
gives unlimited permission to copy, distribute and modify it."

ac_pwd='/home/<USER>/Algo-trade/live-data-fetching/ta-lib'
srcdir='.'
INSTALL='/usr/bin/install -c'
MKDIR_P='/usr/bin/mkdir -p'
# If no file are specified by the user, then we need to provide default
# value.  By we need to know if files were specified by the user.
ac_need_defaults=:
while test $# != 0
do
  case $1 in
  --*=*)
    ac_option=`expr "X$1" : 'X\([^=]*\)='`
    ac_optarg=`expr "X$1" : 'X[^=]*=\(.*\)'`
    ac_shift=:
    ;;
  *)
    ac_option=$1
    ac_optarg=$2
    ac_shift=shift
    ;;
  esac

  case $ac_option in
  # Handling of the options.
  -recheck | --recheck | --rechec | --reche | --rech | --rec | --re | --r)
    ac_cs_recheck=: ;;
  --version | --versio | --versi | --vers | --ver | --ve | --v | -V )
    echo "$ac_cs_version"; exit ;;
  --debug | --debu | --deb | --de | --d | -d )
    debug=: ;;
  --file | --fil | --fi | --f )
    $ac_shift
    CONFIG_FILES="$CONFIG_FILES $ac_optarg"
    ac_need_defaults=false;;
  --header | --heade | --head | --hea )
    $ac_shift
    CONFIG_HEADERS="$CONFIG_HEADERS $ac_optarg"
    ac_need_defaults=false;;
  --he | --h)
    # Conflict between --help and --header
    { echo "$as_me: error: ambiguous option: $1
Try \`$0 --help' for more information." >&2
   { (exit 1); exit 1; }; };;
  --help | --hel | -h )
    echo "$ac_cs_usage"; exit ;;
  -q | -quiet | --quiet | --quie | --qui | --qu | --q \
  | -silent | --silent | --silen | --sile | --sil | --si | --s)
    ac_cs_silent=: ;;

  # This is an error.
  -*) { echo "$as_me: error: unrecognized option: $1
Try \`$0 --help' for more information." >&2
   { (exit 1); exit 1; }; } ;;

  *) ac_config_targets="$ac_config_targets $1"
     ac_need_defaults=false ;;

  esac
  shift
done

ac_configure_extra_args=

if $ac_cs_silent; then
  exec 6>/dev/null
  ac_configure_extra_args="$ac_configure_extra_args --silent"
fi

if $ac_cs_recheck; then
  echo "running CONFIG_SHELL=/bin/bash /bin/bash ./configure " '--prefix=/usr/local' $ac_configure_extra_args " --no-create --no-recursion" >&6
  CONFIG_SHELL=/bin/bash
  export CONFIG_SHELL
  exec /bin/bash "./configure" '--prefix=/usr/local' $ac_configure_extra_args --no-create --no-recursion
fi

exec 5>>config.log
{
  echo
  sed 'h;s/./-/g;s/^.../## /;s/...$/ ##/;p;x;p;x' <<_ASBOX
## Running $as_me. ##
_ASBOX
  echo "$ac_log"
} >&5

#
# INIT-COMMANDS
#
AMDEP_TRUE="" ac_aux_dir="."


# Handling of arguments.
for ac_config_target in $ac_config_targets
do
  case $ac_config_target in
    "include/ta_config.h") CONFIG_HEADERS="$CONFIG_HEADERS include/ta_config.h" ;;
    "depfiles") CONFIG_COMMANDS="$CONFIG_COMMANDS depfiles" ;;
    "Makefile") CONFIG_FILES="$CONFIG_FILES Makefile" ;;
    "src/Makefile") CONFIG_FILES="$CONFIG_FILES src/Makefile" ;;
    "src/ta_abstract/Makefile") CONFIG_FILES="$CONFIG_FILES src/ta_abstract/Makefile" ;;
    "src/ta_common/Makefile") CONFIG_FILES="$CONFIG_FILES src/ta_common/Makefile" ;;
    "src/ta_func/Makefile") CONFIG_FILES="$CONFIG_FILES src/ta_func/Makefile" ;;
    "src/tools/Makefile") CONFIG_FILES="$CONFIG_FILES src/tools/Makefile" ;;
    "src/tools/gen_code/Makefile") CONFIG_FILES="$CONFIG_FILES src/tools/gen_code/Makefile" ;;
    "src/tools/ta_regtest/Makefile") CONFIG_FILES="$CONFIG_FILES src/tools/ta_regtest/Makefile" ;;
    "ta-lib-config") CONFIG_FILES="$CONFIG_FILES ta-lib-config" ;;
    "ta-lib.spec") CONFIG_FILES="$CONFIG_FILES ta-lib.spec" ;;
    "ta-lib.dpkg") CONFIG_FILES="$CONFIG_FILES ta-lib.dpkg" ;;

  *) { { echo "$as_me:$LINENO: error: invalid argument: $ac_config_target" >&5
echo "$as_me: error: invalid argument: $ac_config_target" >&2;}
   { (exit 1); exit 1; }; };;
  esac
done


# If the user did not use the arguments to specify the items to instantiate,
# then the envvar interface is used.  Set only those that are not.
# We use the long form for the default assignment because of an extremely
# bizarre bug on SunOS 4.1.3.
if $ac_need_defaults; then
  test "${CONFIG_FILES+set}" = set || CONFIG_FILES=$config_files
  test "${CONFIG_HEADERS+set}" = set || CONFIG_HEADERS=$config_headers
  test "${CONFIG_COMMANDS+set}" = set || CONFIG_COMMANDS=$config_commands
fi

# Have a temporary directory for convenience.  Make it in the build tree
# simply because there is no reason against having it here, and in addition,
# creating and moving files from /tmp can sometimes cause problems.
# Hook for its removal unless debugging.
# Note that there is a small window in which the directory will not be cleaned:
# after its creation but before its name has been assigned to `$tmp'.
$debug ||
{
  tmp=
  trap 'exit_status=$?
  { test -z "$tmp" || test ! -d "$tmp" || rm -fr "$tmp"; } && exit $exit_status
' 0
  trap '{ (exit 1); exit 1; }' 1 2 13 15
}
# Create a (secure) tmp directory for tmp files.

{
  tmp=`(umask 077 && mktemp -d "./confXXXXXX") 2>/dev/null` &&
  test -n "$tmp" && test -d "$tmp"
}  ||
{
  tmp=./conf$$-$RANDOM
  (umask 077 && mkdir "$tmp")
} ||
{
   echo "$me: cannot create a temporary directory in ." >&2
   { (exit 1); exit 1; }
}

#
# Set up the sed scripts for CONFIG_FILES section.
#

# No need to generate the scripts if there are no CONFIG_FILES.
# This happens for instance when ./config.status config.h
if test -n "$CONFIG_FILES"; then

cat >"$tmp/subs-1.sed" <<\CEOF
/@[a-zA-Z_][a-zA-Z_0-9]*@/!b
s,@SHELL@,|#_!!_#|/bin/bash,g
s,@PATH_SEPARATOR@,|#_!!_#|:,g
s,@PACKAGE_NAME@,|#_!!_#|ta-lib,g
s,@PACKAGE_TARNAME@,|#_!!_#|ta-lib,g
s,@PACKAGE_VERSION@,|#_!!_#|0.4.0,g
s,@PACKAGE_STRING@,|#_!!_#|ta-lib 0.4.0,g
s,@PACKAGE_BUGREPORT@,|#_!!_#|http://sourceforge.net/tracker/?group_id=8903\&atid=108903,g
s,@exec_prefix@,|#_!!_#|${prefix},g
s,@prefix@,|#_!!_#|/usr/local,g
s,@program_transform_name@,|#_!!_#|s\,x\,x\,,g
s,@bindir@,|#_!!_#|${exec_prefix}/bin,g
s,@sbindir@,|#_!!_#|${exec_prefix}/sbin,g
s,@libexecdir@,|#_!!_#|${exec_prefix}/libexec,g
s,@datarootdir@,|#_!!_#|${prefix}/share,g
s,@datadir@,|#_!!_#|${datarootdir},g
s,@sysconfdir@,|#_!!_#|${prefix}/etc,g
s,@sharedstatedir@,|#_!!_#|${prefix}/com,g
s,@localstatedir@,|#_!!_#|${prefix}/var,g
s,@includedir@,|#_!!_#|${prefix}/include,g
s,@oldincludedir@,|#_!!_#|/usr/include,g
s,@docdir@,|#_!!_#|${datarootdir}/doc/${PACKAGE_TARNAME},g
s,@infodir@,|#_!!_#|${datarootdir}/info,g
s,@htmldir@,|#_!!_#|${docdir},g
s,@dvidir@,|#_!!_#|${docdir},g
s,@pdfdir@,|#_!!_#|${docdir},g
s,@psdir@,|#_!!_#|${docdir},g
s,@libdir@,|#_!!_#|${exec_prefix}/lib,g
s,@localedir@,|#_!!_#|${datarootdir}/locale,g
s,@mandir@,|#_!!_#|${datarootdir}/man,g
s,@DEFS@,|#_!!_#|-DHAVE_CONFIG_H,g
s,@ECHO_C@,|#_!!_#|,g
s,@ECHO_N@,|#_!!_#|-n,g
s,@ECHO_T@,|#_!!_#|,g
s,@LIBS@,|#_!!_#|-lpthread -ldl ,g
s,@build_alias@,|#_!!_#|,g
s,@host_alias@,|#_!!_#|,g
s,@target_alias@,|#_!!_#|,g
s,@INSTALL_PROGRAM@,|#_!!_#|${INSTALL},g
s,@INSTALL_SCRIPT@,|#_!!_#|${INSTALL},g
s,@INSTALL_DATA@,|#_!!_#|${INSTALL} -m 644,g
s,@am__isrc@,|#_!!_#|,g
s,@CYGPATH_W@,|#_!!_#|echo,g
s,@PACKAGE@,|#_!!_#|ta-lib,g
s,@VERSION@,|#_!!_#|0.4.0,g
s,@ACLOCAL@,|#_!!_#|${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run aclocal-1.10,g
s,@AUTOCONF@,|#_!!_#|${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run autoconf,g
s,@AUTOMAKE@,|#_!!_#|${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run automake-1.10,g
s,@AUTOHEADER@,|#_!!_#|${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run autoheader,g
s,@MAKEINFO@,|#_!!_#|${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run makeinfo,g
s,@install_sh@,|#_!!_#|$(SHELL) /home/<USER>/Algo-trade/live-data-fetching/ta-lib/install-sh,g
s,@STRIP@,|#_!!_#|strip,g
s,@INSTALL_STRIP_PROGRAM@,|#_!!_#|$(install_sh) -c -s,g
s,@mkdir_p@,|#_!!_#|/usr/bin/mkdir -p,g
s,@AWK@,|#_!!_#|mawk,g
s,@SET_MAKE@,|#_!!_#|,g
s,@am__leading_dot@,|#_!!_#|.,g
s,@AMTAR@,|#_!!_#|${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run tar,g
s,@am__tar@,|#_!!_#|${AMTAR} chof - "$$tardir",g
s,@am__untar@,|#_!!_#|${AMTAR} xf -,g
s,@CC@,|#_!!_#|gcc,g
s,@CFLAGS@,|#_!!_#|-g -O2,g
s,@LDFLAGS@,|#_!!_#|,g
s,@CPPFLAGS@,|#_!!_#|,g
s,@ac_ct_CC@,|#_!!_#|gcc,g
s,@EXEEXT@,|#_!!_#|,g
s,@OBJEXT@,|#_!!_#|o,g
s,@DEPDIR@,|#_!!_#|.deps,g
s,@am__include@,|#_!!_#|include,g
s,@am__quote@,|#_!!_#|,g
s,@AMDEP_TRUE@,|#_!!_#|,g
s,@AMDEP_FALSE@,|#_!!_#|#,g
s,@AMDEPBACKSLASH@,|#_!!_#|\\,g
s,@CCDEPMODE@,|#_!!_#|depmode=gcc3,g
s,@am__fastdepCC_TRUE@,|#_!!_#|,g
s,@am__fastdepCC_FALSE@,|#_!!_#|#,g
s,@build@,|#_!!_#|x86_64-unknown-linux-gnu,g
s,@build_cpu@,|#_!!_#|x86_64,g
s,@build_vendor@,|#_!!_#|unknown,g
s,@build_os@,|#_!!_#|linux-gnu,g
s,@host@,|#_!!_#|x86_64-unknown-linux-gnu,g
s,@host_cpu@,|#_!!_#|x86_64,g
s,@host_vendor@,|#_!!_#|unknown,g
s,@host_os@,|#_!!_#|linux-gnu,g
s,@GREP@,|#_!!_#|/usr/bin/grep,g
s,@EGREP@,|#_!!_#|/usr/bin/grep -E,g
s,@LN_S@,|#_!!_#|ln -s,g
s,@ECHO@,|#_!!_#|echo,g
s,@AR@,|#_!!_#|ar,g
s,@RANLIB@,|#_!!_#|ranlib,g
s,@CPP@,|#_!!_#|gcc -E,g
s,@CXX@,|#_!!_#|g++,g
s,@CXXFLAGS@,|#_!!_#|-g -O2,g
s,@ac_ct_CXX@,|#_!!_#|g++,g
s,@CXXDEPMODE@,|#_!!_#|depmode=gcc3,g
s,@am__fastdepCXX_TRUE@,|#_!!_#|,g
s,@am__fastdepCXX_FALSE@,|#_!!_#|#,g
s,@CXXCPP@,|#_!!_#|g++ -E,g
CEOF
cat >"$tmp/subs-2.sed" <<\CEOF
/@[a-zA-Z_][a-zA-Z_0-9]*@/!b end
s,@F77@,|#_!!_#|,g
s,@FFLAGS@,|#_!!_#|,g
s,@ac_ct_F77@,|#_!!_#|,g
s,@LIBTOOL@,|#_!!_#|$(SHELL) $(top_builddir)/libtool,g
s,@POW_LIB@,|#_!!_#|,g
s,@LIBOBJS@,|#_!!_#|,g
s,@TALIB_LIBRARY_VERSION@,|#_!!_#|0:0:0,g
s,@LTLIBOBJS@,|#_!!_#|,g
:end
s/|#_!!_#|//g
CEOF
fi # test -n "$CONFIG_FILES"


for ac_tag in  :F $CONFIG_FILES  :H $CONFIG_HEADERS    :C $CONFIG_COMMANDS
do
  case $ac_tag in
  :[FHLC]) ac_mode=$ac_tag; continue;;
  esac
  case $ac_mode$ac_tag in
  :[FHL]*:*);;
  :L* | :C*:*) { { echo "$as_me:$LINENO: error: Invalid tag $ac_tag." >&5
echo "$as_me: error: Invalid tag $ac_tag." >&2;}
   { (exit 1); exit 1; }; };;
  :[FH]-) ac_tag=-:-;;
  :[FH]*) ac_tag=$ac_tag:$ac_tag.in;;
  esac
  ac_save_IFS=$IFS
  IFS=:
  set x $ac_tag
  IFS=$ac_save_IFS
  shift
  ac_file=$1
  shift

  case $ac_mode in
  :L) ac_source=$1;;
  :[FH])
    ac_file_inputs=
    for ac_f
    do
      case $ac_f in
      -) ac_f="$tmp/stdin";;
      *) # Look for the file first in the build tree, then in the source tree
	 # (if the path is not absolute).  The absolute path cannot be DOS-style,
	 # because $ac_f cannot contain `:'.
	 test -f "$ac_f" ||
	   case $ac_f in
	   [\\/$]*) false;;
	   *) test -f "$srcdir/$ac_f" && ac_f="$srcdir/$ac_f";;
	   esac ||
	   { { echo "$as_me:$LINENO: error: cannot find input file: $ac_f" >&5
echo "$as_me: error: cannot find input file: $ac_f" >&2;}
   { (exit 1); exit 1; }; };;
      esac
      ac_file_inputs="$ac_file_inputs $ac_f"
    done

    # Let's still pretend it is `configure' which instantiates (i.e., don't
    # use $as_me), people would be surprised to read:
    #    /* config.h.  Generated by config.status.  */
    configure_input="Generated from "`IFS=:
	  echo $* | sed 's|^[^:]*/||;s|:[^:]*/|, |g'`" by configure."
    if test x"$ac_file" != x-; then
      configure_input="$ac_file.  $configure_input"
      { echo "$as_me:$LINENO: creating $ac_file" >&5
echo "$as_me: creating $ac_file" >&6;}
    fi

    case $ac_tag in
    *:-:* | *:-) cat >"$tmp/stdin";;
    esac
    ;;
  esac

  ac_dir=`$as_dirname -- "$ac_file" ||
$as_expr X"$ac_file" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$ac_file" : 'X\(//\)[^/]' \| \
	 X"$ac_file" : 'X\(//\)$' \| \
	 X"$ac_file" : 'X\(/\)' \| . 2>/dev/null ||
echo X"$ac_file" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
  { as_dir="$ac_dir"
  case $as_dir in #(
  -*) as_dir=./$as_dir;;
  esac
  test -d "$as_dir" || { $as_mkdir_p && mkdir -p "$as_dir"; } || {
    as_dirs=
    while :; do
      case $as_dir in #(
      *\'*) as_qdir=`echo "$as_dir" | sed "s/'/'\\\\\\\\''/g"`;; #(
      *) as_qdir=$as_dir;;
      esac
      as_dirs="'$as_qdir' $as_dirs"
      as_dir=`$as_dirname -- "$as_dir" ||
$as_expr X"$as_dir" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$as_dir" : 'X\(//\)[^/]' \| \
	 X"$as_dir" : 'X\(//\)$' \| \
	 X"$as_dir" : 'X\(/\)' \| . 2>/dev/null ||
echo X"$as_dir" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
      test -d "$as_dir" && break
    done
    test -z "$as_dirs" || eval "mkdir $as_dirs"
  } || test -d "$as_dir" || { { echo "$as_me:$LINENO: error: cannot create directory $as_dir" >&5
echo "$as_me: error: cannot create directory $as_dir" >&2;}
   { (exit 1); exit 1; }; }; }
  ac_builddir=.

case "$ac_dir" in
.) ac_dir_suffix= ac_top_builddir_sub=. ac_top_build_prefix= ;;
*)
  ac_dir_suffix=/`echo "$ac_dir" | sed 's,^\.[\\/],,'`
  # A ".." for each directory in $ac_dir_suffix.
  ac_top_builddir_sub=`echo "$ac_dir_suffix" | sed 's,/[^\\/]*,/..,g;s,/,,'`
  case $ac_top_builddir_sub in
  "") ac_top_builddir_sub=. ac_top_build_prefix= ;;
  *)  ac_top_build_prefix=$ac_top_builddir_sub/ ;;
  esac ;;
esac
ac_abs_top_builddir=$ac_pwd
ac_abs_builddir=$ac_pwd$ac_dir_suffix
# for backward compatibility:
ac_top_builddir=$ac_top_build_prefix

case $srcdir in
  .)  # We are building in place.
    ac_srcdir=.
    ac_top_srcdir=$ac_top_builddir_sub
    ac_abs_top_srcdir=$ac_pwd ;;
  [\\/]* | ?:[\\/]* )  # Absolute name.
    ac_srcdir=$srcdir$ac_dir_suffix;
    ac_top_srcdir=$srcdir
    ac_abs_top_srcdir=$srcdir ;;
  *) # Relative name.
    ac_srcdir=$ac_top_build_prefix$srcdir$ac_dir_suffix
    ac_top_srcdir=$ac_top_build_prefix$srcdir
    ac_abs_top_srcdir=$ac_pwd/$srcdir ;;
esac
ac_abs_srcdir=$ac_abs_top_srcdir$ac_dir_suffix


  case $ac_mode in
  :F)
  #
  # CONFIG_FILE
  #

  case $INSTALL in
  [\\/$]* | ?:[\\/]* ) ac_INSTALL=$INSTALL ;;
  *) ac_INSTALL=$ac_top_build_prefix$INSTALL ;;
  esac
  ac_MKDIR_P=$MKDIR_P
  case $MKDIR_P in
  [\\/$]* | ?:[\\/]* ) ;;
  */*) ac_MKDIR_P=$ac_top_build_prefix$MKDIR_P ;;
  esac
# If the template does not know about datarootdir, expand it.
# FIXME: This hack should be removed a few years after 2.60.
ac_datarootdir_hack=; ac_datarootdir_seen=

case `sed -n '/datarootdir/ {
  p
  q
}
/@datadir@/p
/@docdir@/p
/@infodir@/p
/@localedir@/p
/@mandir@/p
' $ac_file_inputs` in
*datarootdir*) ac_datarootdir_seen=yes;;
*@datadir@*|*@docdir@*|*@infodir@*|*@localedir@*|*@mandir@*)
  { echo "$as_me:$LINENO: WARNING: $ac_file_inputs seems to ignore the --datarootdir setting" >&5
echo "$as_me: WARNING: $ac_file_inputs seems to ignore the --datarootdir setting" >&2;}
  ac_datarootdir_hack='
  s&@datadir@&${datarootdir}&g
  s&@docdir@&${datarootdir}/doc/${PACKAGE_TARNAME}&g
  s&@infodir@&${datarootdir}/info&g
  s&@localedir@&${datarootdir}/locale&g
  s&@mandir@&${datarootdir}/man&g
    s&\${datarootdir}&${prefix}/share&g' ;;
esac
  sed "/^[	 ]*VPATH[	 ]*=/{
s/:*\$(srcdir):*/:/
s/:*\${srcdir}:*/:/
s/:*@srcdir@:*/:/
s/^\([^=]*=[	 ]*\):*/\1/
s/:*$//
s/^[^=]*=[	 ]*$//
}

:t
/@[a-zA-Z_][a-zA-Z_0-9]*@/!b
s&@configure_input@&$configure_input&;t t
s&@top_builddir@&$ac_top_builddir_sub&;t t
s&@srcdir@&$ac_srcdir&;t t
s&@abs_srcdir@&$ac_abs_srcdir&;t t
s&@top_srcdir@&$ac_top_srcdir&;t t
s&@abs_top_srcdir@&$ac_abs_top_srcdir&;t t
s&@builddir@&$ac_builddir&;t t
s&@abs_builddir@&$ac_abs_builddir&;t t
s&@abs_top_builddir@&$ac_abs_top_builddir&;t t
s&@INSTALL@&$ac_INSTALL&;t t
s&@MKDIR_P@&$ac_MKDIR_P&;t t
$ac_datarootdir_hack
" $ac_file_inputs | sed -f "$tmp/subs-1.sed" | sed -f "$tmp/subs-2.sed" >$tmp/out

test -z "$ac_datarootdir_hack$ac_datarootdir_seen" &&
  { ac_out=`sed -n '/\${datarootdir}/p' "$tmp/out"`; test -n "$ac_out"; } &&
  { ac_out=`sed -n '/^[	 ]*datarootdir[	 ]*:*=/p' "$tmp/out"`; test -z "$ac_out"; } &&
  { echo "$as_me:$LINENO: WARNING: $ac_file contains a reference to the variable \`datarootdir'
which seems to be undefined.  Please make sure it is defined." >&5
echo "$as_me: WARNING: $ac_file contains a reference to the variable \`datarootdir'
which seems to be undefined.  Please make sure it is defined." >&2;}

  rm -f "$tmp/stdin"
  case $ac_file in
  -) cat "$tmp/out"; rm -f "$tmp/out";;
  *) rm -f "$ac_file"; mv "$tmp/out" $ac_file;;
  esac
 ;;
  :H)
  #
  # CONFIG_HEADER
  #
    # First, check the format of the line:
    cat >"$tmp/defines.sed" <<\CEOF
/^[	 ]*#[	 ]*undef[	 ][	 ]*[_abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ][_abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789]*[	 ]*$/b def
/^[	 ]*#[	 ]*define[	 ][	 ]*[_abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ][_abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789]*[(	 ]/b def
b
:def
s/$/ /
s,^\([	 #]*\)[^	 ]*\([	 ]*PACKAGE_NAME\)[	 (].*,\1define\2 "ta-lib" ,
s,^\([	 #]*\)[^	 ]*\([	 ]*PACKAGE_TARNAME\)[	 (].*,\1define\2 "ta-lib" ,
s,^\([	 #]*\)[^	 ]*\([	 ]*PACKAGE_VERSION\)[	 (].*,\1define\2 "0.4.0" ,
s,^\([	 #]*\)[^	 ]*\([	 ]*PACKAGE_STRING\)[	 (].*,\1define\2 "ta-lib 0.4.0" ,
s,^\([	 #]*\)[^	 ]*\([	 ]*PACKAGE_BUGREPORT\)[	 (].*,\1define\2 "http://sourceforge.net/tracker/?group_id=8903\&atid=108903" ,
s,^\([	 #]*\)[^	 ]*\([	 ]*PACKAGE\)[	 (].*,\1define\2 "ta-lib" ,
s,^\([	 #]*\)[^	 ]*\([	 ]*VERSION\)[	 (].*,\1define\2 "0.4.0" ,
s,^\([	 #]*\)[^	 ]*\([	 ]*STDC_HEADERS\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_SYS_TYPES_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_SYS_STAT_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STDLIB_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRING_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_MEMORY_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRINGS_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_INTTYPES_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STDINT_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_UNISTD_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_DLFCN_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_LIBDL\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_LIBPTHREAD\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*STDC_HEADERS\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_FLOAT_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_INTTYPES_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_LIMITS_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_LOCALE_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STDDEF_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STDINT_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STDLIB_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRING_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_UNISTD_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_WCHAR_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_WCTYPE_H\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_PTRDIFF_T\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*RETSIGTYPE\)[	 (].*,\1define\2 void ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRCOLL\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRFTIME\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_VPRINTF\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_ISASCII\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_LOCALECONV\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_MBLEN\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_MEMMOVE\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_MEMSET\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_MODF\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRCASECMP\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRCHR\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRERROR\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRNCASECMP\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRRCHR\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRSTR\)[	 (].*,\1define\2 1 ,
CEOF
    sed -f "$tmp/defines.sed" $ac_file_inputs >"$tmp/out1"
    # First, check the format of the line:
    cat >"$tmp/defines.sed" <<\CEOF
/^[	 ]*#[	 ]*undef[	 ][	 ]*[_abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ][_abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789]*[	 ]*$/b def
/^[	 ]*#[	 ]*define[	 ][	 ]*[_abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ][_abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789]*[(	 ]/b def
b
:def
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRTOL\)[	 (].*,\1define\2 1 ,
s,^\([	 #]*\)[^	 ]*\([	 ]*HAVE_STRTOUL\)[	 (].*,\1define\2 1 ,
s/ $//
s,^[	 #]*u.*,/* & */,
CEOF
    sed -f "$tmp/defines.sed" "$tmp/out1" >"$tmp/out2"
ac_result="$tmp/out2"
  if test x"$ac_file" != x-; then
    echo "/* $configure_input  */" >"$tmp/config.h"
    cat "$ac_result" >>"$tmp/config.h"
    if diff $ac_file "$tmp/config.h" >/dev/null 2>&1; then
      { echo "$as_me:$LINENO: $ac_file is unchanged" >&5
echo "$as_me: $ac_file is unchanged" >&6;}
    else
      rm -f $ac_file
      mv "$tmp/config.h" $ac_file
    fi
  else
    echo "/* $configure_input  */"
    cat "$ac_result"
  fi
  rm -f "$tmp/out12"
# Compute $ac_file's index in $config_headers.
_am_stamp_count=1
for _am_header in $config_headers :; do
  case $_am_header in
    $ac_file | $ac_file:* )
      break ;;
    * )
      _am_stamp_count=`expr $_am_stamp_count + 1` ;;
  esac
done
echo "timestamp for $ac_file" >`$as_dirname -- $ac_file ||
$as_expr X$ac_file : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X$ac_file : 'X\(//\)[^/]' \| \
	 X$ac_file : 'X\(//\)$' \| \
	 X$ac_file : 'X\(/\)' \| . 2>/dev/null ||
echo X$ac_file |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`/stamp-h$_am_stamp_count
 ;;

  :C)  { echo "$as_me:$LINENO: executing $ac_file commands" >&5
echo "$as_me: executing $ac_file commands" >&6;}
 ;;
  esac


  case $ac_file$ac_mode in
    "depfiles":C) test x"$AMDEP_TRUE" != x"" || for mf in $CONFIG_FILES; do
  # Strip MF so we end up with the name of the file.
  mf=`echo "$mf" | sed -e 's/:.*$//'`
  # Check whether this is an Automake generated Makefile or not.
  # We used to match only the files named `Makefile.in', but
  # some people rename them; so instead we look at the file content.
  # Grep'ing the first line is not enough: some people post-process
  # each Makefile.in and add a new line on top of each file to say so.
  # Grep'ing the whole file is not good either: AIX grep has a line
  # limit of 2048, but all sed's we know have understand at least 4000.
  if sed 10q "$mf" | grep '^#.*generated by automake' > /dev/null 2>&1; then
    dirpart=`$as_dirname -- "$mf" ||
$as_expr X"$mf" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$mf" : 'X\(//\)[^/]' \| \
	 X"$mf" : 'X\(//\)$' \| \
	 X"$mf" : 'X\(/\)' \| . 2>/dev/null ||
echo X"$mf" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
  else
    continue
  fi
  # Extract the definition of DEPDIR, am__include, and am__quote
  # from the Makefile without running `make'.
  DEPDIR=`sed -n 's/^DEPDIR = //p' < "$mf"`
  test -z "$DEPDIR" && continue
  am__include=`sed -n 's/^am__include = //p' < "$mf"`
  test -z "am__include" && continue
  am__quote=`sed -n 's/^am__quote = //p' < "$mf"`
  # When using ansi2knr, U may be empty or an underscore; expand it
  U=`sed -n 's/^U = //p' < "$mf"`
  # Find all dependency output files, they are included files with
  # $(DEPDIR) in their names.  We invoke sed twice because it is the
  # simplest approach to changing $(DEPDIR) to its actual value in the
  # expansion.
  for file in `sed -n "
    s/^$am__include $am__quote\(.*(DEPDIR).*\)$am__quote"'$/\1/p' <"$mf" | \
       sed -e 's/\$(DEPDIR)/'"$DEPDIR"'/g' -e 's/\$U/'"$U"'/g'`; do
    # Make sure the directory exists.
    test -f "$dirpart/$file" && continue
    fdir=`$as_dirname -- "$file" ||
$as_expr X"$file" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$file" : 'X\(//\)[^/]' \| \
	 X"$file" : 'X\(//\)$' \| \
	 X"$file" : 'X\(/\)' \| . 2>/dev/null ||
echo X"$file" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
    { as_dir=$dirpart/$fdir
  case $as_dir in #(
  -*) as_dir=./$as_dir;;
  esac
  test -d "$as_dir" || { $as_mkdir_p && mkdir -p "$as_dir"; } || {
    as_dirs=
    while :; do
      case $as_dir in #(
      *\'*) as_qdir=`echo "$as_dir" | sed "s/'/'\\\\\\\\''/g"`;; #(
      *) as_qdir=$as_dir;;
      esac
      as_dirs="'$as_qdir' $as_dirs"
      as_dir=`$as_dirname -- "$as_dir" ||
$as_expr X"$as_dir" : 'X\(.*[^/]\)//*[^/][^/]*/*$' \| \
	 X"$as_dir" : 'X\(//\)[^/]' \| \
	 X"$as_dir" : 'X\(//\)$' \| \
	 X"$as_dir" : 'X\(/\)' \| . 2>/dev/null ||
echo X"$as_dir" |
    sed '/^X\(.*[^/]\)\/\/*[^/][^/]*\/*$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)[^/].*/{
	    s//\1/
	    q
	  }
	  /^X\(\/\/\)$/{
	    s//\1/
	    q
	  }
	  /^X\(\/\).*/{
	    s//\1/
	    q
	  }
	  s/.*/./; q'`
      test -d "$as_dir" && break
    done
    test -z "$as_dirs" || eval "mkdir $as_dirs"
  } || test -d "$as_dir" || { { echo "$as_me:$LINENO: error: cannot create directory $as_dir" >&5
echo "$as_me: error: cannot create directory $as_dir" >&2;}
   { (exit 1); exit 1; }; }; }
    # echo "creating $dirpart/$file"
    echo '# dummy' > "$dirpart/$file"
  done
done
 ;;

  esac
done # for ac_tag


{ (exit 0); exit 0; }
