#!/usr/bin/env python3
"""
Test script to check DhanHQ API methods and response formats
"""

from dhanhq import dhanhq
import json

# API Configuration
CLIENT_ID = "1105577608"
ACCESS_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

# Initialize Dhan client
dhan = dhanhq(CLIENT_ID, ACCESS_TOKEN)

print("=== DhanHQ API Methods Test ===")

# Check available methods
print("\n1. Available methods in dhanhq object:")
methods = [method for method in dir(dhan) if not method.startswith('_')]
for method in methods:
    print(f"  - {method}")

print("\n2. Testing OHLC Data API:")
try:
    # Test OHLC data for RELIANCE - try different formats
    securities = {
        'NSE_EQ': ['2885']  # RELIANCE
    }

    result = dhan.ohlc_data(securities)
    print(f"OHLC Data Response (format 1):")
    print(json.dumps(result, indent=2))

except Exception as e:
    print(f"OHLC Data Error (format 1): {str(e)}")

try:
    # Try alternative format for quote_data
    securities = {
        'NSE_EQ': ['2885']  # RELIANCE
    }
    result = dhan.quote_data(securities)
    print(f"Quote Data Response:")
    print(json.dumps(result, indent=2))

except Exception as e:
    print(f"Quote Data Error: {str(e)}")

try:
    # Try ticker_data
    result = dhan.ticker_data('NSE_EQ', '2885')
    print(f"Ticker Data Response:")
    print(json.dumps(result, indent=2))

except Exception as e:
    print(f"Ticker Data Error: {str(e)}")

print("\n3. Testing Historical Intraday Data API:")
try:
    # Test historical intraday data for RELIANCE - try with different parameters
    print("Testing with interval='5':")
    result = dhan.intraday_minute_data(
        security_id='2885',
        exchange_segment='NSE_EQ',
        instrument_type='EQUITY',
        interval='5',
        from_date='2024-12-26',
        to_date='2024-12-26'
    )
    print(f"Historical Intraday Response:")
    print(json.dumps(result, indent=2))

except Exception as e:
    print(f"Historical Intraday Error: {str(e)}")

try:
    # Test with today's date
    print("Testing with today's date:")
    result = dhan.intraday_minute_data(
        security_id='2885',
        exchange_segment='NSE_EQ',
        instrument_type='EQUITY',
        interval='5',
        from_date='2025-06-05',
        to_date='2025-06-05'
    )
    print(f"Today's Intraday Response:")
    print(json.dumps(result, indent=2))

except Exception as e:
    print(f"Today's Intraday Error: {str(e)}")

print("\n4. Testing Historical Daily Data API:")
try:
    # Test historical daily data for RELIANCE
    result = dhan.historical_daily_data(
        security_id='2885',
        exchange_segment='NSE_EQ',
        instrument_type='EQUITY',
        expiry_code=0,
        from_date='2024-12-20',
        to_date='2024-12-26'
    )
    print(f"Historical Daily Response:")
    print(json.dumps(result, indent=2))
    
except Exception as e:
    print(f"Historical Daily Error: {str(e)}")

print("\n5. Testing LTP Data API:")
try:
    # Test LTP data for RELIANCE
    securities = {
        'NSE_EQ': ['2885']  # RELIANCE
    }
    
    result = dhan.ltp_data(securities)
    print(f"LTP Data Response:")
    print(json.dumps(result, indent=2))
    
except Exception as e:
    print(f"LTP Data Error: {str(e)}")
