import os
import time
import datetime
import pandas as pd
import logging
from Dhan_Tradehull import Tradehull

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('live_data_fetcher.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

client_code = "1105577608"
token_id = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

# Initialize Tradehull client
tsl = Tradehull(client_code, token_id)

# Configuration
symbol = 'NIFTY'
exchange = 'INDEX'
timeframe = "5"  # 1-minute candles
output_file = f'{symbol}_{exchange}_{timeframe}min_live.csv'

# Market timing configuration (IST)
IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))  # UTC+5:30
MARKET_OPEN_TIME = datetime.time(9, 15)
MARKET_CLOSE_TIME = datetime.time(15, 30)

# API configuration
MAX_RETRIES = 3
RETRY_DELAY = 2  # seconds
API_BUFFER_TIME = 15  # seconds to wait for API data availability
CANDLE_FETCH_DELAY = 10  # additional seconds after minute completion

# Initialize CSV file with headers if it doesn't exist
def initialize_csv_file():
    if not os.path.exists(output_file):
        headers = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume', 'timestamp'])
        headers.to_csv(output_file, index=False)
        logger.info(f"Created new file: {output_file}")
    else:
        # Check if existing file has data from today
        try:
            existing_data = pd.read_csv(output_file)
            if not existing_data.empty:
                existing_data['timestamp'] = pd.to_datetime(existing_data['timestamp'])
                today = get_current_time_ist().date()
                today_data = existing_data[existing_data['timestamp'].dt.date == today]

                if not today_data.empty:
                    logger.info(f"Found existing file with {len(today_data)} candles from today")
                    logger.info(f"Data range: {today_data['timestamp'].min()} to {today_data['timestamp'].max()}")
                else:
                    logger.info(f"Existing file found but no data from today - will fetch historical data")
            else:
                logger.info(f"Empty file found - will fetch historical data")
        except Exception as e:
            logger.warning(f"Error reading existing file: {e} - will recreate")

initialize_csv_file()
logger.info(f"Starting live data collection for {symbol} {timeframe}min candles...")

# Utility functions
def get_current_time_ist():
    """Get current time in IST timezone"""
    return datetime.datetime.now(IST)

def is_market_open():
    """Check if market is currently open"""
    current_time = get_current_time_ist().time()
    current_date = get_current_time_ist().date()

    # Check if it's a weekday (Monday=0, Sunday=6)
    is_weekday = current_date.weekday() < 5

    # Check if current time is within market hours
    is_trading_hours = MARKET_OPEN_TIME <= current_time <= MARKET_CLOSE_TIME

    return is_weekday and is_trading_hours

def api_call_with_retry(func, *args, **kwargs):
    """Execute API call with retry logic"""
    for attempt in range(MAX_RETRIES):
        try:
            logger.debug(f"API call attempt {attempt + 1}/{MAX_RETRIES}")
            result = func(*args, **kwargs)

            if result is not None and not result.empty:
                logger.debug(f"API call successful on attempt {attempt + 1}")
                return result
            else:
                logger.warning(f"API call returned empty data on attempt {attempt + 1}")

        except Exception as e:
            logger.error(f"API call failed on attempt {attempt + 1}: {str(e)}")

        if attempt < MAX_RETRIES - 1:
            logger.info(f"Retrying in {RETRY_DELAY} seconds...")
            time.sleep(RETRY_DELAY)

    logger.error(f"All {MAX_RETRIES} API call attempts failed")
    return pd.DataFrame()

def wait_until_next_minute():
    """Wait until the start of the next minute with proper buffer time"""
    current_time = get_current_time_ist()
    current_timestamp = pd.Timestamp(current_time)
    next_minute = (current_timestamp + pd.Timedelta(minutes=1)).floor('min')

    # Calculate sleep time with buffer
    sleep_seconds = (next_minute - current_timestamp).total_seconds() + CANDLE_FETCH_DELAY

    logger.info(f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')} IST")
    logger.info(f"Waiting {sleep_seconds:.1f} seconds until {next_minute.strftime('%H:%M:00')} + {CANDLE_FETCH_DELAY}s buffer...")

    time.sleep(sleep_seconds)

def fetch_and_process_historical_data():
    """Fetch all historical data from market open to current time"""
    logger.info("Fetching historical data from market open to current time...")

    # Fetch the historical data with retry logic
    chart = api_call_with_retry(
        tsl.get_historical_data,
        tradingsymbol=symbol,
        exchange=exchange,
        timeframe=timeframe
    )

    if chart.empty:
        logger.warning("No historical data received from API")
        return False

    logger.info(f"Received historical data with {len(chart)} rows")

    # Convert timestamp to datetime for comparison
    chart['timestamp'] = pd.to_datetime(chart['timestamp'])

    # Normalize all timestamps to be timezone-naive for consistent comparison
    if chart['timestamp'].dt.tz is not None:
        # Convert to IST and then remove timezone info
        chart['timestamp'] = chart['timestamp'].dt.tz_convert(IST)
        chart['timestamp'] = chart['timestamp'].dt.tz_localize(None)

    # Get today's date
    today = get_current_time_ist().date()

    # Filter data for today only (from market open to current time)
    today_start = datetime.datetime.combine(today, MARKET_OPEN_TIME)
    current_time = get_current_time_ist().replace(tzinfo=None)

    # Filter chart data for today's trading session
    today_data = chart[
        (chart['timestamp'] >= today_start) &
        (chart['timestamp'] <= current_time)
    ].copy()

    if today_data.empty:
        logger.warning("No data found for today's trading session")
        return False

    logger.info(f"Found {len(today_data)} candles for today's session")
    logger.info(f"Data range: {today_data['timestamp'].min()} to {today_data['timestamp'].max()}")

    # Save all historical data to CSV (overwrite existing file)
    today_data.to_csv(output_file, index=False)
    logger.info(f"✅ Saved {len(today_data)} historical candles to {output_file}")

    # Show first and last few candles
    logger.info("First 3 candles:")
    for _, candle in today_data.head(3).iterrows():
        logger.info(f"  {candle['timestamp'].strftime('%H:%M')}: O={candle['open']:.2f}, H={candle['high']:.2f}, L={candle['low']:.2f}, C={candle['close']:.2f}")

    logger.info("Last 3 candles:")
    for _, candle in today_data.tail(3).iterrows():
        logger.info(f"  {candle['timestamp'].strftime('%H:%M')}: O={candle['open']:.2f}, H={candle['high']:.2f}, L={candle['low']:.2f}, C={candle['close']:.2f}")

    return True

def find_and_save_candle(target_time):
    """Find and save candle data for the target time"""
    logger.info(f"Fetching candle data for {target_time.strftime('%Y-%m-%d %H:%M:00')} IST...")

    # Fetch the historical data with retry logic
    chart = api_call_with_retry(
        tsl.get_historical_data,
        tradingsymbol=symbol,
        exchange=exchange,
        timeframe=timeframe
    )

    if chart.empty:
        logger.warning("No data received from API")
        return False

    logger.debug(f"Received data with {len(chart)} rows")

    # Convert timestamp to datetime for comparison
    chart['timestamp'] = pd.to_datetime(chart['timestamp'])

    # Normalize all timestamps to be timezone-naive for consistent comparison
    if chart['timestamp'].dt.tz is not None:
        # Convert to IST and then remove timezone info
        chart['timestamp'] = chart['timestamp'].dt.tz_convert(IST)
        chart['timestamp'] = chart['timestamp'].dt.tz_localize(None)

    # Ensure target_time is timezone-naive
    target_timestamp = pd.Timestamp(target_time)
    if target_timestamp.tz is not None:
        target_timestamp = target_timestamp.tz_localize(None)

    # Find the candle for the target minute
    target_candles = chart[chart['timestamp'].dt.floor('min') == target_timestamp.floor('min')]

    if not target_candles.empty:
        # Check if this candle already exists in our CSV
        if os.path.exists(output_file):
            existing_data = pd.read_csv(output_file)
            if not existing_data.empty:
                existing_data['timestamp'] = pd.to_datetime(existing_data['timestamp'])
                existing_timestamps = existing_data['timestamp'].dt.floor('min')

                if target_timestamp.floor('min') in existing_timestamps.values:
                    logger.info(f"Candle for {target_time.strftime('%H:%M')} already exists, skipping...")
                    return True

        # Add to CSV file (append mode)
        target_candles.to_csv(output_file, mode='a', header=False, index=False)

        candle_data = target_candles.iloc[0]
        logger.info(f"✅ Added candle for {target_time.strftime('%H:%M')}: "
                   f"O={candle_data['open']:.2f}, H={candle_data['high']:.2f}, "
                   f"L={candle_data['low']:.2f}, C={candle_data['close']:.2f}, "
                   f"V={candle_data['volume']}")
        return True
    else:
        logger.warning(f"No candle data found for {target_time.strftime('%H:%M')}")

        # Show available timestamps for debugging
        available_times = chart['timestamp'].dt.floor('min').unique()
        logger.debug(f"Available timestamps: {len(available_times)} candles")

        if len(available_times) > 0:
            latest_time = available_times.max()
            logger.info(f"Most recent candle available: {latest_time}")

            # If we're looking for a very recent candle, try the latest available
            # Both timestamps should now be timezone-naive
            time_diff = abs((target_timestamp.floor('min') - pd.Timestamp(latest_time)).total_seconds())
            if time_diff <= 120:  # Within 2 minutes
                latest_candle = chart[chart['timestamp'].dt.floor('min') == latest_time].iloc[0]
                logger.info(f"Using latest available candle: "
                           f"O={latest_candle['open']:.2f}, H={latest_candle['high']:.2f}, "
                           f"L={latest_candle['low']:.2f}, C={latest_candle['close']:.2f}")

        return False

# Main loop to fetch and save data every minute
def main_loop():
    """Main data collection loop"""
    logger.info("Starting main data collection loop...")

    # Flag to track if we've fetched historical data
    historical_data_fetched = False

    while True:
        try:
            # Check if market is open
            if not is_market_open():
                current_time = get_current_time_ist()
                logger.info(f"Market is closed at {current_time.strftime('%Y-%m-%d %H:%M:%S')} IST")
                logger.info("Waiting for market to open...")
                time.sleep(60)  # Check every minute
                continue

            # Fetch historical data once when market is open (if not already done)
            if not historical_data_fetched:
                logger.info("🔄 First time market is open - fetching all historical data from 9:15 AM...")
                success = fetch_and_process_historical_data()
                if success:
                    historical_data_fetched = True
                    logger.info("✅ Historical data fetch completed. Now starting live data collection...")
                else:
                    logger.warning("Failed to fetch historical data, will retry...")
                    time.sleep(30)
                    continue

            # Wait until the start of the next minute
            wait_until_next_minute()

            # Get current time in IST
            current_time = get_current_time_ist()

            # Calculate the previous minute (the completed candle we want)
            current_timestamp = pd.Timestamp(current_time)
            previous_minute = current_timestamp - pd.Timedelta(minutes=1)

            # Try to find and save the candle
            success = find_and_save_candle(previous_minute)

            if not success:
                logger.warning("Failed to fetch candle data, will retry next minute")

        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, stopping...")
            break
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            logger.info(f"Retrying in {RETRY_DELAY * 5} seconds...")
            time.sleep(RETRY_DELAY * 5)

if __name__ == "__main__":
    try:
        main_loop()
    except KeyboardInterrupt:
        logger.info("Data collection stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
    finally:
        logger.info("Live data collection ended")
