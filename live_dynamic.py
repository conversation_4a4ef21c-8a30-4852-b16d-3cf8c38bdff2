import os
import time
import datetime
import pandas as pd
from Dhan_Tradehull import Tradehull

client_code = "1105577608"
token_id = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

tsl = Tradehull(client_code, token_id)

# Define the symbol to track
symbol = 'NIFTY'
exchange = 'INDEX'
timeframe = "1"  # 1-minute candles
output_file = f'{symbol}_{exchange}_{timeframe}min_live.csv'

# Initialize CSV file with headers if it doesn't exist
if not os.path.exists(output_file):
    headers = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume', 'timestamp'])
    headers.to_csv(output_file, index=False)
    print(f"Created new file: {output_file}")

print(f"Starting live data collection for {symbol} {timeframe}min candles...")

# Function to wait until the start of the next minute
def wait_until_next_minute():
    current_time = pd.Timestamp.now()
    next_minute = (current_time + pd.Timedelta(minutes=1)).floor('min')
    sleep_seconds = (next_minute - current_time).total_seconds()
    
    print(f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Waiting {sleep_seconds:.1f} seconds until {next_minute.strftime('%H:%M:00')}...")
    
    time.sleep(sleep_seconds + 5)  # Add 5 seconds buffer to ensure the candle is complete

# Main loop to fetch and save data every minute
while True:
    try:
        # Wait until the start of the next minute
        wait_until_next_minute()
        
        # Get current time (should be the start of a new minute)
        current_time = pd.Timestamp.now()
        
        # Calculate the previous minute (the completed candle we want)
        previous_minute = (current_time - pd.Timedelta(minutes=1)).floor('min')
        
        print(f"Fetching candle data for {previous_minute.strftime('%Y-%m-%d %H:%M:00')}...")
        
        # Fetch the historical data
        chart = tsl.get_historical_data(tradingsymbol=symbol, exchange=exchange, timeframe=timeframe)
        
        if not chart.empty:
            # Print the first few rows of the data for debugging
            print(f"Received data with {len(chart)} rows. First few rows:")
            print(chart.head())
            
            # Convert timestamp to datetime for comparison
            chart['timestamp'] = pd.to_datetime(chart['timestamp'])
            
            # Find the candle for the previous minute
            previous_candle = chart[chart['timestamp'].dt.floor('min') == previous_minute]
            
            if not previous_candle.empty:
                # Add to CSV file (append mode)
                previous_candle.to_csv(output_file, mode='a', header=False, index=False)
                
                candle_data = previous_candle.iloc[0]
                print(f"Added candle for {previous_minute.strftime('%H:%M')}: Open={candle_data['open']}, High={candle_data['high']}, Low={candle_data['low']}, Close={candle_data['close']}")
            else:
                print(f"No candle data found for {previous_minute.strftime('%H:%M')}")
                print("Available timestamps in data:")
                print(chart['timestamp'].dt.floor('min').unique())
                
                # Try to find the closest available candle
                if len(chart) > 0:
                    closest_candle = chart.iloc[-1]
                    print(f"Most recent candle available is for: {closest_candle['timestamp']}")
                    print(f"OHLC: Open={closest_candle['open']}, High={closest_candle['high']}, Low={closest_candle['low']}, Close={closest_candle['close']}")
        else:
            print("No data received from API")
            # Check if market is open
            current_hour = current_time.hour
            current_minute = current_time.minute
            is_market_hours = (9 <= current_hour < 16) or (current_hour == 16 and current_minute <= 30)
            is_weekday = current_time.weekday() < 5  # Monday to Friday
            
            if not is_market_hours:
                print("Note: Current time is outside regular market hours (9:15 AM - 3:30 PM)")
            if not is_weekday:
                print("Note: Today is not a trading day (weekend)")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        print("Retrying in 30 seconds...")
        time.sleep(30)
