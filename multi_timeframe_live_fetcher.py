#!/usr/bin/env python3
"""
Multi-Timeframe Live Data Fetcher
- Parallel data collection for 1min, 5min, and 60min timeframes
- Historical data backfill from market open (9:15 AM) to current time
- Intelligent duplicate detection and data management
- Optimized buffer times for different timeframes
"""

import os
import time
import datetime
import pandas as pd
import numpy as np
import logging
import threading
from concurrent.futures import ThreadPoolExecutor
from collections import deque
from Dhan_Tradehull import Tradehull

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s',
    handlers=[
        logging.FileHandler('multi_timeframe_fetcher.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# API Configuration
client_code = "1105577608"
token_id = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

# Initialize Tradehull client
tsl = Tradehull(client_code, token_id)

# Trading Configuration
SYMBOL = 'NIFTY'
EXCHANGE = 'INDEX'

# Market timing configuration (IST)
IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))  # UTC+5:30
MARKET_OPEN_TIME = datetime.time(9, 15)
MARKET_CLOSE_TIME = datetime.time(15, 30)

# Timeframe configurations with optimized buffer times and historical data requirements
TIMEFRAMES = {
    '1': {
        'name': '1min',
        'interval_minutes': 1,
        'buffer_seconds': 10,  # 10 seconds after candle completion
        'wait_interval': 60,   # Check every minute
        'historical_days': 3,  # 3 days of historical data
        'max_candles': 3 * 375  # 3 days * 375 candles per day (9:15-15:30)
    },
    '5': {
        'name': '5min',
        'interval_minutes': 5,
        'buffer_seconds': 30,  # 30 seconds after candle completion
        'wait_interval': 60,   # Check every minute
        'historical_days': 7,  # 7 days of historical data
        'max_candles': 7 * 75   # 7 days * 75 candles per day (375/5)
    },
    '60': {
        'name': '60min',
        'interval_minutes': 60,
        'buffer_seconds': 120, # 2 minutes after candle completion
        'wait_interval': 300,  # Check every 5 minutes
        'historical_days': 30, # 30 market days of historical data
        'max_candles': 30 * 6   # 30 days * 6 candles per day (375/60)
    }
}

# API configuration
MAX_RETRIES = 3
RETRY_DELAY = 2  # seconds

# Thread-safe lock for API calls
api_lock = threading.Lock()

# Global data storage using deque for efficient rolling windows
historical_data_storage = {
    '1': deque(maxlen=TIMEFRAMES['1']['max_candles']),
    '5': deque(maxlen=TIMEFRAMES['5']['max_candles']),
    '60': deque(maxlen=TIMEFRAMES['60']['max_candles'])
}

def get_current_time_ist():
    """Get current time in IST timezone"""
    return datetime.datetime.now(IST)

def is_market_open():
    """Check if market is currently open"""
    current_time = get_current_time_ist().time()
    current_date = get_current_time_ist().date()
    
    # Check if it's a weekday (Monday=0, Sunday=6)
    is_weekday = current_date.weekday() < 5
    
    # Check if current time is within market hours
    is_trading_hours = MARKET_OPEN_TIME <= current_time <= MARKET_CLOSE_TIME
    
    return is_weekday and is_trading_hours

def get_output_filename(timeframe, file_type='live'):
    """Generate output filename for timeframe with proper folder structure"""
    tf_name = TIMEFRAMES[timeframe]["name"]

    if file_type == 'historical':
        # Create Historical Data Layer folder structure
        folder = "Historical_Data_Layer"
        if not os.path.exists(folder):
            os.makedirs(folder)
        return os.path.join(folder, f'historical_{tf_name}.csv')
    else:
        # Create Live Data Layer folder structure
        folder = "Live_Data_Layer"
        if not os.path.exists(folder):
            os.makedirs(folder)
        return os.path.join(folder, f'{SYMBOL}_{EXCHANGE}_{tf_name}_live.csv')

def get_market_days_back(days):
    """Calculate the date that is 'days' market days back from today"""
    current_date = get_current_time_ist().date()
    market_days_count = 0
    check_date = current_date

    logger.info(f"Calculating {days} market days back from {current_date}")

    while market_days_count < days:
        check_date -= datetime.timedelta(days=1)
        # Check if it's a weekday (Monday=0, Sunday=6)
        if check_date.weekday() < 5:  # Monday to Friday
            market_days_count += 1
            logger.debug(f"Market day {market_days_count}: {check_date}")

    logger.info(f"Start date for {days} market days: {check_date}")
    return check_date

def api_call_with_retry(tf_key, func, **kwargs):
    """Execute API call with retry logic and thread safety"""
    with api_lock:  # Ensure thread-safe API calls
        for attempt in range(MAX_RETRIES):
            try:
                logger.debug(f"[{TIMEFRAMES[tf_key]['name']}] API call attempt {attempt + 1}/{MAX_RETRIES}")
                result = func(**kwargs)

                if result is not None and not result.empty:
                    logger.debug(f"[{TIMEFRAMES[tf_key]['name']}] API call successful on attempt {attempt + 1}")
                    return result
                else:
                    logger.warning(f"[{TIMEFRAMES[tf_key]['name']}] API call returned empty data on attempt {attempt + 1}")

            except Exception as e:
                logger.error(f"[{TIMEFRAMES[tf_key]['name']}] API call failed on attempt {attempt + 1}: {str(e)}")

            if attempt < MAX_RETRIES - 1:
                logger.info(f"[{TIMEFRAMES[tf_key]['name']}] Retrying in {RETRY_DELAY} seconds...")
                time.sleep(RETRY_DELAY)

        logger.error(f"[{TIMEFRAMES[tf_key]['name']}] All {MAX_RETRIES} API call attempts failed")
        return pd.DataFrame()

def initialize_csv_files(timeframe):
    """Initialize both live and historical CSV files for timeframe"""
    tf_name = TIMEFRAMES[timeframe]['name']

    # Initialize live data file
    live_file = get_output_filename(timeframe, 'live')
    historical_file = get_output_filename(timeframe, 'historical')

    # Create files if they don't exist
    for file_path, file_type in [(live_file, 'live'), (historical_file, 'historical')]:
        if not os.path.exists(file_path):
            headers = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume', 'timestamp'])
            headers.to_csv(file_path, index=False)
            logger.info(f"[{tf_name}] Created new {file_type} file: {file_path}")

    # Load existing historical data into deque
    has_historical_data = load_historical_data_to_deque(timeframe)

    # Check live data
    has_live_data = False
    try:
        if os.path.exists(live_file):
            existing_data = pd.read_csv(live_file)
            if not existing_data.empty:
                existing_data['timestamp'] = pd.to_datetime(existing_data['timestamp'])
                today = get_current_time_ist().date()
                today_data = existing_data[existing_data['timestamp'].dt.date == today]

                if not today_data.empty:
                    logger.info(f"[{tf_name}] Found existing live file with {len(today_data)} candles from today")
                    has_live_data = True
    except Exception as e:
        logger.warning(f"[{tf_name}] Error reading existing live file: {e}")

    return has_historical_data, has_live_data

def load_historical_data_to_deque(timeframe):
    """Load existing historical data into deque"""
    tf_name = TIMEFRAMES[timeframe]['name']
    historical_file = get_output_filename(timeframe, 'historical')

    try:
        if os.path.exists(historical_file):
            df = pd.read_csv(historical_file)
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                # Sort by timestamp to ensure proper order
                df = df.sort_values('timestamp')

                # Clear existing deque and load data
                historical_data_storage[timeframe].clear()
                for _, row in df.iterrows():
                    historical_data_storage[timeframe].append(row.to_dict())

                logger.info(f"[{tf_name}] Loaded {len(df)} historical candles into memory")
                logger.info(f"[{tf_name}] Historical data range: {df['timestamp'].min()} to {df['timestamp'].max()}")
                return True
    except Exception as e:
        logger.warning(f"[{tf_name}] Error loading historical data: {e}")

    return False

def fetch_comprehensive_historical_data(timeframe):
    """Fetch comprehensive historical data based on timeframe requirements"""
    tf_name = TIMEFRAMES[timeframe]['name']
    historical_days = TIMEFRAMES[timeframe]['historical_days']

    logger.info(f"[{tf_name}] Fetching {historical_days} {'market days' if timeframe == '60' else 'calendar days'} of historical data...")

    # Calculate start date based on market days
    if timeframe == '60':  # 30 market days for 60min
        start_date = get_market_days_back(historical_days)
        logger.info(f"[{tf_name}] Using market days calculation: {historical_days} market days = from {start_date}")
    else:  # Calendar days for 1min and 5min
        start_date = get_current_time_ist().date() - datetime.timedelta(days=historical_days)
        logger.info(f"[{tf_name}] Using calendar days calculation: {historical_days} days = from {start_date}")

    logger.info(f"[{tf_name}] Fetching data from {start_date} to today")

    # Fetch the historical data with retry logic
    chart = api_call_with_retry(
        timeframe,
        tsl.get_historical_data,
        tradingsymbol=SYMBOL,
        exchange=EXCHANGE,
        timeframe=timeframe
    )

    if chart.empty:
        logger.warning(f"[{tf_name}] No historical data received from API")
        return False

    logger.info(f"[{tf_name}] Received historical data with {len(chart)} rows")

    # Convert timestamp to datetime for comparison
    chart['timestamp'] = pd.to_datetime(chart['timestamp'])

    # Normalize all timestamps to be timezone-naive for consistent comparison
    if chart['timestamp'].dt.tz is not None:
        chart['timestamp'] = chart['timestamp'].dt.tz_convert(IST)
        chart['timestamp'] = chart['timestamp'].dt.tz_localize(None)

    # Filter data from start_date onwards
    start_datetime = datetime.datetime.combine(start_date, MARKET_OPEN_TIME)
    current_time = get_current_time_ist().replace(tzinfo=None)

    # Filter chart data for the required period
    filtered_data = chart[
        (chart['timestamp'] >= start_datetime) &
        (chart['timestamp'] <= current_time)
    ].copy()

    if filtered_data.empty:
        logger.warning(f"[{tf_name}] No data found for the specified period")
        # For 60min, try to get all available data if filtered data is empty
        if timeframe == '60':
            logger.info(f"[{tf_name}] Trying to get all available data for 60min timeframe...")
            filtered_data = chart.copy()
            if not filtered_data.empty:
                logger.info(f"[{tf_name}] Using all available data: {len(filtered_data)} candles")
            else:
                return False
        else:
            return False

    # Sort by timestamp
    filtered_data = filtered_data.sort_values('timestamp')

    # For 60min timeframe, ensure we have enough data by taking the most recent candles
    if timeframe == '60':
        max_candles = TIMEFRAMES[timeframe]['max_candles']
        if len(filtered_data) > max_candles:
            filtered_data = filtered_data.tail(max_candles)
            logger.info(f"[{tf_name}] Limited to most recent {max_candles} candles for rolling window")

    logger.info(f"[{tf_name}] Found {len(filtered_data)} candles for {historical_days} {'market days' if timeframe == '60' else 'calendar days'}")
    logger.info(f"[{tf_name}] Data range: {filtered_data['timestamp'].min()} to {filtered_data['timestamp'].max()}")

    # Save to historical CSV file
    historical_file = get_output_filename(timeframe, 'historical')
    filtered_data.to_csv(historical_file, index=False)
    logger.info(f"[{tf_name}] ✅ Saved {len(filtered_data)} historical candles to {historical_file}")

    # Load into deque for efficient access
    historical_data_storage[timeframe].clear()
    for _, row in filtered_data.iterrows():
        historical_data_storage[timeframe].append(row.to_dict())

    # Show first and last few candles
    logger.info(f"[{tf_name}] First 3 candles:")
    for _, candle in filtered_data.head(3).iterrows():
        logger.info(f"[{tf_name}]   {candle['timestamp'].strftime('%Y-%m-%d %H:%M')}: O={candle['open']:.2f}, H={candle['high']:.2f}, L={candle['low']:.2f}, C={candle['close']:.2f}")

    logger.info(f"[{tf_name}] Last 3 candles:")
    for _, candle in filtered_data.tail(3).iterrows():
        logger.info(f"[{tf_name}]   {candle['timestamp'].strftime('%Y-%m-%d %H:%M')}: O={candle['open']:.2f}, H={candle['high']:.2f}, L={candle['low']:.2f}, C={candle['close']:.2f}")

    # Detect supply/demand zones for historical data
    if len(filtered_data) >= 10:
        logger.info(f"[{tf_name}] 🔍 Analyzing historical data for supply/demand zones...")
        data_list = [row.to_dict() for _, row in filtered_data.iterrows()]
        analysis_result = calculate_wma_signals(data_list)

        if analysis_result and analysis_result['zones']:
            logger.info(f"[{tf_name}] 📊 Found {len(analysis_result['zones'])} supply/demand zones in historical data")
            append_zones_to_csv(timeframe, analysis_result['zones'])

            # Log zone summary
            supply_zones = [z for z in analysis_result['zones'] if z['zone_type'] == 'SUPPLY']
            demand_zones = [z for z in analysis_result['zones'] if z['zone_type'] == 'DEMAND']

            logger.info(f"[{tf_name}] 🔴 Supply zones: {len(supply_zones)}, 🟢 Demand zones: {len(demand_zones)}")
        else:
            logger.info(f"[{tf_name}] No supply/demand zones detected in historical data")

    return True

def fetch_and_process_today_data(timeframe):
    """Fetch today's data for live CSV file"""
    tf_name = TIMEFRAMES[timeframe]['name']
    logger.info(f"[{tf_name}] Fetching today's data for live file...")

    # Fetch the historical data with retry logic
    chart = api_call_with_retry(
        timeframe,
        tsl.get_historical_data,
        tradingsymbol=SYMBOL,
        exchange=EXCHANGE,
        timeframe=timeframe
    )

    if chart.empty:
        logger.warning(f"[{tf_name}] No data received from API")
        return False

    # Convert timestamp to datetime for comparison
    chart['timestamp'] = pd.to_datetime(chart['timestamp'])

    # Normalize all timestamps to be timezone-naive for consistent comparison
    if chart['timestamp'].dt.tz is not None:
        chart['timestamp'] = chart['timestamp'].dt.tz_convert(IST)
        chart['timestamp'] = chart['timestamp'].dt.tz_localize(None)

    # Get today's date
    today = get_current_time_ist().date()

    # Filter data for today only (from market open to current time)
    today_start = datetime.datetime.combine(today, MARKET_OPEN_TIME)
    current_time = get_current_time_ist().replace(tzinfo=None)

    # Filter chart data for today's trading session
    today_data = chart[
        (chart['timestamp'] >= today_start) &
        (chart['timestamp'] <= current_time)
    ].copy()

    if today_data.empty:
        logger.warning(f"[{tf_name}] No data found for today's trading session")
        return False

    logger.info(f"[{tf_name}] Found {len(today_data)} candles for today's session")

    # Save to live CSV file (overwrite existing file)
    live_file = get_output_filename(timeframe, 'live')
    today_data.to_csv(live_file, index=False)
    logger.info(f"[{tf_name}] ✅ Saved {len(today_data)} today's candles to {live_file}")

    return True

def should_fetch_candle(timeframe, current_time):
    """Determine if we should fetch a candle for this timeframe at current time"""
    interval_minutes = TIMEFRAMES[timeframe]['interval_minutes']

    # For 1min: fetch every minute
    if interval_minutes == 1:
        return True

    # For 5min: fetch at 5, 10, 15, 20, etc. minute marks
    if interval_minutes == 5:
        return current_time.minute % 5 == 0

    # For 60min: fetch at the top of each hour
    if interval_minutes == 60:
        return current_time.minute == 0

    return False

def get_target_candle_time(timeframe, current_time):
    """Get the target candle time based on timeframe"""
    interval_minutes = TIMEFRAMES[timeframe]['interval_minutes']
    current_timestamp = pd.Timestamp(current_time)

    if interval_minutes == 1:
        # For 1min: get previous minute
        return current_timestamp - pd.Timedelta(minutes=1)
    elif interval_minutes == 5:
        # For 5min: get the previous 5-minute boundary
        minutes_past = current_time.minute % 5
        if minutes_past == 0:
            # If exactly on boundary, get previous 5min candle
            return current_timestamp - pd.Timedelta(minutes=5)
        else:
            # Round down to previous 5min boundary
            return current_timestamp.floor('5min')
    elif interval_minutes == 60:
        # For 60min: get previous hour
        if current_time.minute == 0:
            return current_timestamp - pd.Timedelta(hours=1)
        else:
            return current_timestamp.floor('H')

    return current_timestamp

def find_and_save_candle(timeframe, target_time):
    """Find and save candle data for the target time"""
    tf_name = TIMEFRAMES[timeframe]['name']
    logger.info(f"[{tf_name}] Fetching candle data for {target_time.strftime('%Y-%m-%d %H:%M:00')} IST...")

    # Fetch the historical data with retry logic
    chart = api_call_with_retry(
        timeframe,
        tsl.get_historical_data,
        tradingsymbol=SYMBOL,
        exchange=EXCHANGE,
        timeframe=timeframe
    )

    if chart.empty:
        logger.warning(f"[{tf_name}] No data received from API")
        return False

    logger.debug(f"[{tf_name}] Received data with {len(chart)} rows")

    # Convert timestamp to datetime for comparison
    chart['timestamp'] = pd.to_datetime(chart['timestamp'])

    # Normalize all timestamps to be timezone-naive for consistent comparison
    if chart['timestamp'].dt.tz is not None:
        chart['timestamp'] = chart['timestamp'].dt.tz_convert(IST)
        chart['timestamp'] = chart['timestamp'].dt.tz_localize(None)

    # Ensure target_time is timezone-naive
    target_timestamp = pd.Timestamp(target_time)
    if target_timestamp.tz is not None:
        target_timestamp = target_timestamp.tz_localize(None)

    # Find the candle for the target time (use appropriate floor based on timeframe)
    if timeframe == '1':
        target_candles = chart[chart['timestamp'].dt.floor('min') == target_timestamp.floor('min')]
    elif timeframe == '5':
        target_candles = chart[chart['timestamp'].dt.floor('5min') == target_timestamp.floor('5min')]
    elif timeframe == '60':
        target_candles = chart[chart['timestamp'].dt.floor('H') == target_timestamp.floor('H')]
    else:
        target_candles = chart[chart['timestamp'].dt.floor('min') == target_timestamp.floor('min')]

    if not target_candles.empty:
        candle_data = target_candles.iloc[0]
        candle_dict = candle_data.to_dict()

        # Check if this candle already exists in live CSV
        live_file = get_output_filename(timeframe, 'live')
        candle_exists = False

        if os.path.exists(live_file):
            existing_data = pd.read_csv(live_file)
            if not existing_data.empty:
                existing_data['timestamp'] = pd.to_datetime(existing_data['timestamp'])

                # Check for existing timestamp
                target_floor = target_timestamp.floor('5min' if timeframe == '5' else ('H' if timeframe == '60' else 'min'))
                existing_floors = existing_data['timestamp'].dt.floor('5min' if timeframe == '5' else ('H' if timeframe == '60' else 'min'))

                if target_floor in existing_floors.values:
                    logger.info(f"[{tf_name}] Candle for {target_time.strftime('%H:%M')} already exists in live file, skipping...")
                    candle_exists = True

        if not candle_exists:
            # Add to live CSV file (append mode)
            target_candles.to_csv(live_file, mode='a', header=False, index=False)
            logger.info(f"[{tf_name}] ✅ Added candle to live file for {target_time.strftime('%H:%M')}: "
                       f"O={candle_data['open']:.2f}, H={candle_data['high']:.2f}, "
                       f"L={candle_data['low']:.2f}, C={candle_data['close']:.2f}, "
                       f"V={candle_data['volume']}")

        # Add to historical deque (this will automatically maintain the rolling window)
        historical_data_storage[timeframe].append(candle_dict)

        # Update historical CSV file with current deque data
        update_historical_csv(timeframe)

        return True
    else:
        logger.warning(f"[{tf_name}] No candle data found for {target_time.strftime('%H:%M')}")

        # Show available timestamps for debugging
        available_times = chart['timestamp'].unique()
        logger.debug(f"[{tf_name}] Available timestamps: {len(available_times)} candles")

        if len(available_times) > 0:
            latest_time = available_times.max()
            logger.info(f"[{tf_name}] Most recent candle available: {latest_time}")

        return False

def calculate_wma(prices, period):
    """Calculate Weighted Moving Average"""
    wma_values = []

    for i in range(len(prices)):
        if i < period - 1:
            wma_values.append(np.nan)
        else:
            # Get the last 'period' prices
            window_prices = prices[i - period + 1:i + 1]

            # Create weights (1, 2, 3, ..., period)
            weights = np.arange(1, period + 1)

            # Calculate weighted average
            wma = np.sum(window_prices * weights) / np.sum(weights)
            wma_values.append(wma)

    return np.array(wma_values)

def calculate_wma_signals(data):
    """Calculate WMA signals and detect supply/demand zones"""
    if len(data) < 10:  # Need at least 10 candles for 10 WMA
        return None

    # Convert to numpy arrays
    close_prices = np.array([float(candle['close']) for candle in data])
    high_prices = np.array([float(candle['high']) for candle in data])
    low_prices = np.array([float(candle['low']) for candle in data])

    # Calculate WMAs using custom function
    wma5 = calculate_wma(close_prices, 5)
    wma10 = calculate_wma(close_prices, 10)

    # Find crossover points
    signals = []
    zones = []

    last_signal = None
    last_signal_index = None

    for i in range(10, len(data)):  # Start from index 10 (after WMA10 is valid)
        current_wma5 = wma5[i]
        current_wma10 = wma10[i]
        prev_wma5 = wma5[i-1]
        prev_wma10 = wma10[i-1]

        # Skip if any WMA value is NaN
        if np.isnan(current_wma5) or np.isnan(current_wma10) or np.isnan(prev_wma5) or np.isnan(prev_wma10):
            continue

        # Detect crossovers
        signal_type = None

        # Up signal: 5 WMA crosses above 10 WMA
        if prev_wma5 <= prev_wma10 and current_wma5 > current_wma10:
            signal_type = 'UP'
        # Down signal: 5 WMA crosses below 10 WMA
        elif prev_wma5 >= prev_wma10 and current_wma5 < current_wma10:
            signal_type = 'DOWN'

        if signal_type:
            signal_data = {
                'index': i,
                'timestamp': data[i]['timestamp'],
                'signal': signal_type,
                'wma5': current_wma5,
                'wma10': current_wma10,
                'candle_data': data[i]
            }
            signals.append(signal_data)

            # Create zone if we have a previous signal
            if last_signal and last_signal_index is not None:
                zone_data = create_supply_demand_zone(
                    data, last_signal, signal_type, last_signal_index, i,
                    high_prices, low_prices
                )
                if zone_data:
                    zones.append(zone_data)

            last_signal = signal_type
            last_signal_index = i

    return {
        'signals': signals,
        'zones': zones,
        'wma5': wma5,
        'wma10': wma10
    }

def create_supply_demand_zone(data, prev_signal, current_signal, start_idx, end_idx, high_prices, low_prices):
    """Create supply or demand zone between two signals"""

    # Supply zone: Between UP and DOWN signals (use highest high)
    if prev_signal == 'UP' and current_signal == 'DOWN':
        zone_high = np.max(high_prices[start_idx:end_idx+1])
        zone_type = 'SUPPLY'
        zone_level = zone_high

    # Demand zone: Between DOWN and UP signals (use lowest low)
    elif prev_signal == 'DOWN' and current_signal == 'UP':
        zone_low = np.min(low_prices[start_idx:end_idx+1])
        zone_type = 'DEMAND'
        zone_level = zone_low
    else:
        return None

    return {
        'zone_type': zone_type,
        'zone_level': zone_level,
        'start_timestamp': data[start_idx]['timestamp'],
        'end_timestamp': data[end_idx]['timestamp'],
        'start_signal': prev_signal,
        'end_signal': current_signal,
        'candle_count': end_idx - start_idx + 1
    }

def append_zones_to_csv(timeframe, zones):
    """Append supply/demand zones to CSV files"""
    if not zones:
        return

    tf_name = TIMEFRAMES[timeframe]['name']

    # Create zones CSV filename
    zones_folder = "Supply_Demand_Zones"
    if not os.path.exists(zones_folder):
        os.makedirs(zones_folder)

    zones_file = os.path.join(zones_folder, f'zones_{tf_name}.csv')

    # Create zones DataFrame
    zones_df = pd.DataFrame(zones)

    # Check if file exists and append or create
    if os.path.exists(zones_file):
        # Read existing data to avoid duplicates
        existing_zones = pd.read_csv(zones_file)
        if not existing_zones.empty:
            existing_zones['start_timestamp'] = pd.to_datetime(existing_zones['start_timestamp'])
            existing_zones['end_timestamp'] = pd.to_datetime(existing_zones['end_timestamp'])

            # Filter out zones that already exist
            zones_df['start_timestamp'] = pd.to_datetime(zones_df['start_timestamp'])
            zones_df['end_timestamp'] = pd.to_datetime(zones_df['end_timestamp'])

            # Check for duplicates based on timestamps and zone type
            new_zones = []
            for _, zone in zones_df.iterrows():
                is_duplicate = False
                for _, existing in existing_zones.iterrows():
                    if (zone['start_timestamp'] == existing['start_timestamp'] and
                        zone['end_timestamp'] == existing['end_timestamp'] and
                        zone['zone_type'] == existing['zone_type']):
                        is_duplicate = True
                        break

                if not is_duplicate:
                    new_zones.append(zone.to_dict())

            if new_zones:
                new_zones_df = pd.DataFrame(new_zones)
                new_zones_df.to_csv(zones_file, mode='a', header=False, index=False)
                logger.info(f"[{tf_name}] ✅ Added {len(new_zones)} new zones to {zones_file}")
            else:
                logger.info(f"[{tf_name}] No new zones to add (all zones already exist)")
        else:
            zones_df.to_csv(zones_file, index=False)
            logger.info(f"[{tf_name}] ✅ Created zones file with {len(zones_df)} zones: {zones_file}")
    else:
        # Create new file with headers
        zones_df.to_csv(zones_file, index=False)
        logger.info(f"[{tf_name}] ✅ Created new zones file with {len(zones_df)} zones: {zones_file}")

def update_historical_csv(timeframe):
    """Update historical CSV file with current deque data and detect zones"""
    tf_name = TIMEFRAMES[timeframe]['name']
    historical_file = get_output_filename(timeframe, 'historical')

    try:
        # Convert deque to DataFrame
        if historical_data_storage[timeframe]:
            df = pd.DataFrame(list(historical_data_storage[timeframe]))
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.sort_values('timestamp')

            # Save to historical CSV file
            df.to_csv(historical_file, index=False)
            logger.debug(f"[{tf_name}] Updated historical CSV with {len(df)} candles")

            # Detect supply/demand zones if we have enough data
            if len(historical_data_storage[timeframe]) >= 10:
                logger.debug(f"[{tf_name}] Analyzing supply/demand zones...")
                analysis_result = calculate_wma_signals(list(historical_data_storage[timeframe]))

                if analysis_result and analysis_result['zones']:
                    logger.info(f"[{tf_name}] Found {len(analysis_result['zones'])} supply/demand zones")
                    append_zones_to_csv(timeframe, analysis_result['zones'])

                    # Log zone details
                    for zone in analysis_result['zones']:
                        logger.info(f"[{tf_name}] {zone['zone_type']} zone at {zone['zone_level']:.2f} "
                                   f"({zone['start_timestamp']} to {zone['end_timestamp']})")

    except Exception as e:
        logger.error(f"[{tf_name}] Error updating historical CSV or detecting zones: {e}")

def timeframe_worker(timeframe):
    """Worker function for each timeframe - runs in separate thread"""
    tf_name = TIMEFRAMES[timeframe]['name']
    logger.info(f"[{tf_name}] Starting timeframe worker...")

    # Initialize CSV files and check for existing data
    has_historical_data, has_live_data = initialize_csv_files(timeframe)

    # Determine what data needs to be fetched
    historical_data_fetched = has_historical_data
    live_data_fetched = has_live_data

    while True:
        try:
            # Check if market is open
            if not is_market_open():
                current_time = get_current_time_ist()
                logger.info(f"[{tf_name}] Market is closed at {current_time.strftime('%Y-%m-%d %H:%M:%S')} IST")

                # If market is closed and we don't have comprehensive historical data, fetch it
                if not historical_data_fetched:
                    logger.info(f"[{tf_name}] 🔄 Market closed - fetching comprehensive historical data...")
                    success = fetch_comprehensive_historical_data(timeframe)
                    if success:
                        historical_data_fetched = True
                        logger.info(f"[{tf_name}] ✅ Comprehensive historical data fetch completed")
                    else:
                        logger.warning(f"[{tf_name}] Failed to fetch comprehensive historical data")

                # If we don't have today's live data, fetch it
                if not live_data_fetched:
                    logger.info(f"[{tf_name}] 🔄 Fetching today's data for live file...")
                    success = fetch_and_process_today_data(timeframe)
                    if success:
                        live_data_fetched = True
                        logger.info(f"[{tf_name}] ✅ Today's data fetch completed")

                # Wait before checking again
                time.sleep(60)  # Check every minute
                continue

            # Fetch comprehensive historical data once when market is open (if not already done)
            if not historical_data_fetched:
                logger.info(f"[{tf_name}] 🔄 First time market is open - fetching comprehensive historical data...")
                success = fetch_comprehensive_historical_data(timeframe)
                if success:
                    historical_data_fetched = True
                    logger.info(f"[{tf_name}] ✅ Comprehensive historical data fetch completed")
                else:
                    logger.warning(f"[{tf_name}] Failed to fetch comprehensive historical data, will retry...")
                    time.sleep(30)
                    continue

            # Fetch today's data if not already done
            if not live_data_fetched:
                logger.info(f"[{tf_name}] 🔄 Fetching today's data for live file...")
                success = fetch_and_process_today_data(timeframe)
                if success:
                    live_data_fetched = True
                    logger.info(f"[{tf_name}] ✅ Today's data fetch completed. Starting live collection...")
                else:
                    logger.warning(f"[{tf_name}] Failed to fetch today's data, will retry...")
                    time.sleep(30)
                    continue

            # Get current time
            current_time = get_current_time_ist()

            # Check if we should fetch a candle for this timeframe
            if should_fetch_candle(timeframe, current_time):
                # Wait for buffer time to ensure candle is complete
                buffer_seconds = TIMEFRAMES[timeframe]['buffer_seconds']
                logger.info(f"[{tf_name}] Candle boundary reached, waiting {buffer_seconds}s buffer...")
                time.sleep(buffer_seconds)

                # Get the target candle time
                target_time = get_target_candle_time(timeframe, current_time)

                # Try to find and save the candle
                success = find_and_save_candle(timeframe, target_time)

                if not success:
                    logger.warning(f"[{tf_name}] Failed to fetch candle data")

            # Wait based on timeframe interval
            wait_interval = TIMEFRAMES[timeframe]['wait_interval']
            time.sleep(wait_interval)

        except KeyboardInterrupt:
            logger.info(f"[{tf_name}] Received keyboard interrupt, stopping...")
            break
        except Exception as e:
            logger.error(f"[{tf_name}] Unexpected error: {str(e)}")
            logger.info(f"[{tf_name}] Retrying in {RETRY_DELAY * 5} seconds...")
            time.sleep(RETRY_DELAY * 5)

def main():
    """Main function to start all timeframe workers in parallel"""
    logger.info("🚀 Starting Multi-Timeframe Live Data Fetcher...")
    logger.info(f"📊 Timeframes: {', '.join([TIMEFRAMES[tf]['name'] for tf in TIMEFRAMES.keys()])}")
    logger.info(f"📈 Symbol: {SYMBOL} ({EXCHANGE})")
    logger.info(f"⏰ Market Hours: {MARKET_OPEN_TIME} - {MARKET_CLOSE_TIME} IST")

    # Create thread pool for parallel execution
    with ThreadPoolExecutor(max_workers=len(TIMEFRAMES), thread_name_prefix="TF") as executor:
        # Submit each timeframe worker to the thread pool
        futures = []
        for timeframe in TIMEFRAMES.keys():
            future = executor.submit(timeframe_worker, timeframe)
            futures.append(future)
            logger.info(f"✅ Started worker for {TIMEFRAMES[timeframe]['name']}")

        try:
            # Wait for all workers to complete (they run indefinitely)
            for future in futures:
                future.result()
        except KeyboardInterrupt:
            logger.info("🛑 Received keyboard interrupt, shutting down all workers...")
            executor.shutdown(wait=False)
        except Exception as e:
            logger.error(f"❌ Fatal error in main: {str(e)}")
        finally:
            logger.info("🔚 Multi-timeframe data collection ended")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Data collection stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
    finally:
        logger.info("Application terminated")
