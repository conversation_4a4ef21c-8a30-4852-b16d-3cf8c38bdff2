from dhanhq import dhanhq
import pandas as pd
import json

# API Configuration
client_code = "1105577608"
token_id = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

print("🔧 Testing DhanHQ API Connection...")

try:
    # Initialize DhanHQ client
    dhan = dhanhq(client_code, token_id)
    print("✅ DhanHQ client initialized successfully")

    # Test API call with error handling
    print("\n📊 Fetching NIFTY INDEX data...")

    response = dhan.intraday_minute_data(
        security_id=13,
        exchange_segment="IDX_I",
        instrument_type="INDEX",
        from_date="2025-06-01",
        to_date="2025-06-05"
    )

    print(f"📋 API Response type: {type(response)}")
    print(f"📋 API Response keys: {response.keys() if isinstance(response, dict) else 'Not a dict'}")

    # Check if response contains data
    if isinstance(response, dict) and "data" in response:
        data = response["data"]
        print(f"📊 Data type: {type(data)}")
        print(f"📊 Data length: {len(data) if hasattr(data, '__len__') else 'No length'}")

        if data and isinstance(data, list) and len(data) > 0:
            print(f"📊 First record: {data[0]}")

            # Create DataFrame
            index_data = pd.DataFrame(data)
            print(f"✅ DataFrame created with shape: {index_data.shape}")

            # Convert timestamp if column exists
            if 'timestamp' in index_data.columns:
                index_data['timestamp'] = index_data['timestamp'].apply(
                    lambda x: dhan.convert_to_date_time(x)
                )
                print("✅ Timestamps converted successfully")

            # Display sample data
            print("\n📈 Sample Data:")
            print(index_data.head())

            # Display data info
            print("\n📊 Data Info:")
            print(index_data.info())

        else:
            print("❌ No data returned or data is empty")
            print(f"Data content: {data}")
    else:
        print("❌ Invalid response format")
        print(f"Response: {response}")

except Exception as e:
    print(f"❌ Error occurred: {str(e)}")
    print(f"Error type: {type(e).__name__}")

    # Additional debugging
    try:
        print("\n🔍 Testing basic API connectivity...")
        dhan_test = dhanhq(client_code, token_id)
        print("✅ Basic client creation successful")
    except Exception as basic_error:
        print(f"❌ Basic client creation failed: {basic_error}")

print("\n🎯 Test completed!")