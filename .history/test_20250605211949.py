from dhanhq import dhanhq
import pandas as pd
client_code = "1105577608"
token_id = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

dhan = dhanhq(client_code, token_id)

index_data = pd.DataFrame(dhan.intraday_minute_data(security_id=13, exchange_segment="IDX_I", instrument_type="INDEX", from_date="2025-03-01", to_date="2025-0-31")["data"])
index_data['timestamp'] = index_data['timestamp'].apply(lambda x: dhan.convert_to_date_time(x))

print(index_data)