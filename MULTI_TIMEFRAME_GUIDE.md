# Multi-Timeframe Live Data Fetcher - Complete Guide

## Overview
The `multi_timeframe_live_fetcher.py` script provides parallel data collection for **1min, 5min, and 60min** timeframes simultaneously. It intelligently handles historical data backfill and continues with live data collection.

## Key Features

### ✅ **Parallel Processing**
- **3 separate threads** running simultaneously
- **1min, 5min, 60min** timeframes collected in parallel
- **Thread-safe API calls** with proper locking
- **Independent error handling** for each timeframe

### ✅ **Intelligent Data Management**
- **Historical backfill**: Fetches all data from 9:15 AM to current time
- **Duplicate detection**: Skips already existing candles
- **Market hours awareness**: Handles market open/close scenarios
- **Automatic recovery**: Continues after temporary failures

### ✅ **Optimized Buffer Times**
Different timeframes have optimized buffer times for data availability:
- **1min**: 10 seconds buffer
- **5min**: 30 seconds buffer  
- **60min**: 2 minutes buffer

### ✅ **Smart Scheduling**
- **1min**: Fetches every minute
- **5min**: Fetches at 5, 10, 15, 20, etc. minute marks
- **60min**: Fetches at the top of each hour

## File Outputs

The script creates separate CSV files for each timeframe:
- `NIFTY_INDEX_1min_live.csv` - 1-minute candles
- `NIFTY_INDEX_5min_live.csv` - 5-minute candles
- `NIFTY_INDEX_60min_live.csv` - 60-minute candles

## Usage

### Running the Script
```bash
# Using UV environment (recommended)
uv run python multi_timeframe_live_fetcher.py

# Or using regular Python
python3 multi_timeframe_live_fetcher.py
```

### Expected Behavior

#### **During Market Hours (9:15 AM - 3:30 PM)**
1. **First Run**: Fetches all historical data from 9:15 AM to current time
2. **Live Collection**: Continues collecting new candles as they complete
3. **Parallel Processing**: All timeframes work simultaneously

#### **After Market Close**
1. **Historical Data**: Fetches complete day's data if not already present
2. **No Duplicates**: Skips fetching if data already exists
3. **Waiting Mode**: Waits for next market open

#### **Weekend/Holidays**
1. **Data Preservation**: Maintains existing data files
2. **Waiting Mode**: Waits for next trading day

## Log Output Examples

### Startup
```
🚀 Starting Multi-Timeframe Live Data Fetcher...
📊 Timeframes: 1min, 5min, 60min
📈 Symbol: NIFTY (INDEX)
⏰ Market Hours: 09:15:00 - 15:30:00 IST
✅ Started worker for 1min
✅ Started worker for 5min
✅ Started worker for 60min
```

### Historical Data Fetch
```
[1min] 🔄 First time market is open - fetching historical data...
[1min] Received historical data with 1029 rows
[1min] Found 279 candles for today's session
[1min] ✅ Saved 279 historical candles to NIFTY_INDEX_1min_live.csv

[5min] Found 56 candles for today's session
[5min] ✅ Saved 56 historical candles to NIFTY_INDEX_5min_live.csv

[60min] Found 5 candles for today's session
[60min] ✅ Saved 5 historical candles to NIFTY_INDEX_60min_live.csv
```

### Live Data Collection
```
[1min] ✅ Added candle for 13:45: O=24730.95, H=24736.40, L=24728.75, C=24732.55
[5min] ✅ Added candle for 13:45: O=24725.30, H=24738.15, L=24720.10, C=24732.55
[60min] ✅ Added candle for 13:00: O=24796.90, H=24862.30, L=24711.35, C=24732.55
```

## Configuration

### Timeframe Settings
```python
TIMEFRAMES = {
    '1': {
        'name': '1min',
        'interval_minutes': 1,
        'buffer_seconds': 10,  # 10 seconds after candle completion
        'wait_interval': 60    # Check every minute
    },
    '5': {
        'name': '5min',
        'interval_minutes': 5,
        'buffer_seconds': 30,  # 30 seconds after candle completion
        'wait_interval': 60    # Check every minute
    },
    '60': {
        'name': '60min',
        'interval_minutes': 60,
        'buffer_seconds': 120, # 2 minutes after candle completion
        'wait_interval': 300   # Check every 5 minutes
    }
}
```

### Buffer Time Rationale

**Why different buffer times?**

1. **1min candles**: Complete quickly, 10 seconds is sufficient
2. **5min candles**: Need more time for aggregation, 30 seconds buffer
3. **60min candles**: Complex aggregation, 2 minutes buffer ensures data availability

## Performance Characteristics

### **Resource Usage**
- **CPU**: Low to moderate (3 threads)
- **Memory**: Minimal (processes data in chunks)
- **Network**: Optimized with thread-safe API calls
- **Disk**: Sequential writes to separate CSV files

### **Data Accuracy**
- **Complete Coverage**: No missing candles during market hours
- **Duplicate Prevention**: Intelligent detection and skipping
- **Error Recovery**: Automatic retry with exponential backoff

### **Scalability**
- **Thread Pool**: Efficient parallel processing
- **API Rate Limiting**: Built-in protection with locks
- **Memory Efficient**: Processes data in batches

## Troubleshooting

### Common Issues

1. **"No data received from API"**
   - Check internet connectivity
   - Verify Dhan credentials
   - Check if market is open

2. **"Candle already exists, skipping"**
   - Normal behavior, prevents duplicates
   - Indicates script is working correctly

3. **Thread synchronization issues**
   - Script uses thread-safe locks
   - Each timeframe operates independently

### Log Analysis
- **INFO**: Normal operation status
- **WARNING**: Recoverable issues (missing candles, retries)
- **ERROR**: Serious issues requiring attention

## Advanced Features

### **Intelligent Scheduling**
- Automatically determines when to fetch each timeframe
- Optimizes API calls based on candle completion times
- Handles edge cases (market open/close, weekends)

### **Data Integrity**
- Validates timestamp consistency
- Handles timezone conversions properly
- Maintains data quality across all timeframes

### **Fault Tolerance**
- Individual thread failures don't affect others
- Automatic recovery from temporary network issues
- Graceful handling of API rate limits

## Comparison with Single Timeframe

| Feature | Single TF | Multi TF |
|---------|-----------|----------|
| **Timeframes** | 1 | 3 parallel |
| **Files Created** | 1 CSV | 3 CSV files |
| **Resource Usage** | Lower | Moderate |
| **Data Coverage** | Limited | Comprehensive |
| **Flexibility** | Basic | Advanced |

## Next Steps

1. **Monitor Performance**: Check log files for any issues
2. **Verify Data**: Confirm all CSV files are being updated
3. **Customize Settings**: Adjust buffer times if needed
4. **Add More Symbols**: Extend to other trading instruments
5. **Database Integration**: Consider storing data in database for better performance

The multi-timeframe fetcher provides a robust, scalable solution for comprehensive market data collection across multiple time horizons simultaneously.
